# MessageHOC - FlatList Implementation

## Overview
The MessageHOC component has been refactored from a complex gesture-based horizontal swiping system to a FlatList-based approach for better performance and UX.

## Key Implementation Details

### Files Modified
- **MessageHOC.tsx** - Main container with horizontal FlatList for message swiping
- **HtmlTab.tsx** - Fixed layout shifts by removing conditional styling
- **MessageScreen.tsx** - Enhanced with isDummy prop for performance optimization

### Core Features Implemented

#### 1. FlatList Configuration
- **Horizontal scrolling** with `pagingEnabled` for smooth message-to-message swiping
- **Performance optimizations**:
  - `windowSize={21}` - Renders 21 items at once to prevent white screens
  - `maxToRenderPerBatch={5}` - Batch rendering for smooth scrolling
  - `removeClippedSubviews={false}` - Prevents virtualization issues
  - `initialNumToRender={emailIds.length < 10 ? emailIds.length : 10}` - Smart initial rendering

#### 2. Skeleton Loading States
- **MessageSkeleton component** - Accurate representation of actual MessageScreen layout
- **Loading strategy**: Shows skeleton only for genuinely missing messages (`!itemMessage`)
- **Prevents white screens** during fast swiping and initial load

#### 3. Smart Message Rendering
- **Dummy vs Full messages**: Non-current messages render as simplified dummy versions
- **CommentsHOC integration**: Only current message gets full CommentsHOC wrapper
- **Type safety**: Proper TypeScript interfaces and prop optimization

#### 4. Enhanced Debounced Preloading Strategy
- **Adjacent message preloading**: Automatically loads previous and next messages
- **Smart data fetching**: Only fetches missing `fullMessageBody` content
- **Redux integration**: Efficient state management for message data
- **Robust debounced loading**: 
  - 300ms delay prevents API spam during fast swiping
  - Timer cleanup prevents memory leaks on component unmount
  - Single-source API calls eliminates duplicate requests
  - Initial load separated from swiping logic

### Performance Optimizations

#### FlatList Settings
```typescript
windowSize={31}                    // Increased to prevent white screens during fast swiping
maxToRenderPerBatch={8}           // Increased batch size for smoother scrolling
updateCellsBatchingPeriod={100}   // Faster updates during rapid scrolling
removeClippedSubviews={false}     // Prevent virtualization artifacts
initialNumToRender={emailIds.length}  // Render all items initially
disableVirtualization={true}      // Disable virtualization to prevent blank screens
```

#### Prop Optimization
- **Static handlers memoization**: Prevents unnecessary re-renders
- **Dummy-specific props**: Separate memoized props for simplified dummy messages
- **Callback dependencies**: Carefully managed to prevent excessive re-renders
- **Enhanced debounced API calls**: 
  - 300ms timeout prevents multiple API calls during fast swiping
  - Debounce timer ref for proper cleanup
  - Separated initial load from swipe handling
  - Single responsibility for API calls during navigation

### Key Learnings & Solutions

#### Issue: White Screens During Fast Swiping
**Solution**: Implemented comprehensive skeleton loading with realistic layout structure

#### Issue: Layout Shifts in HtmlTab
**Solution**: Removed conditional styling that changed during loading states (MessageHOC.tsx:264-267)

#### Issue: Skeleton Showing on Every Swipe
**Solution**: Simplified skeleton condition to only show for missing Redux data (`!itemMessage || !itemMessage.fullMessageBody`)

#### Issue: Skeleton Showing Even When Message Body Exists
**Solution**: Removed `getMessageBodyLoading[UMS_Guid]` check from skeleton condition to prevent showing skeleton when content already exists but API call is in progress

#### Issue: TypeScript Errors with 'as any'
**Solution**: Proper type imports and `as unknown as MessageScreenProps` casting

#### Issue: Duplicate API Calls on Component Mount
**Solution**: Fixed useEffect dependencies and coordinated initial load with viewable items handler to prevent redundant calls

#### Issue: Infinite Loop in API Calls  
**Solution**: Removed problematic `loadMessageData` useCallback that created dependency loop with `messages.byId`

#### Issue: Multiple API Calls During Fast Swiping
**Solution**: Implemented comprehensive debounced loading system:
- **Debounced handleViewableItemsChanged**: 300ms delay prevents API spam during fast swiping
- **Removed conflicting useEffect**: Eliminated duplicate API calls from initial load useEffect that fired on every `currentUMS_Guid` change
- **Single-source API calls**: Only the debounced handler makes API calls during swiping, initial load useEffect only runs once on mount
- **Timer cleanup**: Proper cleanup of debounce timers to prevent memory leaks

#### Issue: Skeleton Loading Despite Existing Message Body
**Solution**: Fixed skeleton condition logic to prevent unnecessary loading screens:
- **Problem**: `getMessageBodyLoading[UMS_Guid]` could be `true` even when `fullMessageBody` already existed, causing skeletons to show during API calls for already-loaded content
- **Root Cause**: Debounced API handler dispatches `getMessageBody` action even for messages with existing content, temporarily setting loading flag
- **Fix**: Changed skeleton condition from `(!itemMessage || (itemMessage && !itemMessage.fullMessageBody && getMessageBodyLoading[UMS_Guid]))` to `(!itemMessage || !itemMessage.fullMessageBody)`
- **Result**: Skeletons now only appear when content is genuinely missing, not during redundant API calls

### Testing Commands
```bash
# Run linting (adjust command based on project setup)
npm run lint

# Run type checking
npm run typecheck

# Run tests
npm test
```

### Future Considerations
1. **Bundle size monitoring** - Watch for increased bundle size from skeleton implementation
2. **Memory usage** - Monitor with larger email lists (100+ messages) with virtualization disabled
3. **Animation polish** - Consider adding subtle transitions for skeleton appearances
4. **Error handling** - Enhanced error states for network failures during preloading
5. **Debounce timing** - May need to adjust 300ms delay based on user feedback and network conditions

### Performance Metrics to Monitor
- **Time to interactive** on message selection from list
- **Memory usage** with large email lists (virtualization disabled)
- **Smooth scrolling** frame rate during fast swiping
- **Network efficiency** of debounced preloading strategy
- **API call frequency** during fast swiping (should be minimal with enhanced debouncing)
- **Timer cleanup effectiveness** to prevent memory leaks
- **Initial load performance** vs subsequent navigation loads

## Architecture Notes
- **Redux state management** for message data and loading states
- **React Native FlatList** for virtualized horizontal scrolling
- **react-content-loader/native** for skeleton loading states
- **TypeScript** for type safety and better developer experience