import React from "react";
import { Controller, useForm } from "react-hook-form";
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  StyleSheet,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import { FormData } from "../../containers/LoginHOC";

// Components
import {
  type Theme,
  useThemeAwareObject,
  CustomText,
  Button,
  FONT_SIZES,
  SPACING,
  Input,
  TextWithDuration,
  Logo,
} from "b-ui-lib";

type Props = {
  baseUrl?: string;
  username?: string;
  password?: string;
  loginErrorMessage?: string;
  isLoading: boolean;
  onSubmit: (formData: FormData) => void;
  clearErrorMessage: () => void;
};

const LoginScreen: React.FC = ({
  baseUrl,
  username,
  password,
  loginErrorMessage,
  isLoading,
  onSubmit,
  clearErrorMessage,
}: Props) => {
  const { images, styles } = useThemeAwareObject(createStyles);

  const LOGIN_FIELDS = {
    baseUrl: "baseUrl",
    username: "username",
    password: "password",
  };

  const DEFAULT_VALUES = {
    [LOGIN_FIELDS.baseUrl]: baseUrl,
    [LOGIN_FIELDS.username]: username,
    [LOGIN_FIELDS.password]: password,
  };

  const { control, handleSubmit } = useForm({
    defaultValues: DEFAULT_VALUES,
  });

  const inputIos = {
    paddingVertical: SPACING.XS,
  };

  return (
    <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.container}
      >
        <CustomText style={styles.title}>Benefit Software</CustomText>

        <View style={styles.body}>
          <Logo source={images?.B_SIGNATURE_LOGO} style={styles.logo} />

          <View style={styles.form}>
            <View style={styles.formBody}>
              <Controller
                control={control}
                rules={{
                  required: {
                    value: true,
                    message: "Company Prefix is required",
                  },
                  pattern: {
                    value: /^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/i,
                    message: "Enter a valid URL",
                  },
                }}
                render={({
                  field: { onChange, value },
                  fieldState: { error },
                }) => (
                  <Input
                    inputStyle={Platform.OS === "ios" ? inputIos : {}}
                    placeholder="Company Prefix"
                    value={value}
                    onChangeText={onChange}
                    error={error}
                  />
                )}
                name={LOGIN_FIELDS.baseUrl}
              />

              <Controller
                control={control}
                rules={{
                  required: { value: true, message: "Username is required" },
                }}
                render={({
                  field: { onChange, value },
                  fieldState: { error },
                }) => (
                  <Input
                    placeholder="Usesrname"
                    value={value}
                    onChangeText={onChange}
                    error={error}
                  />
                )}
                name={LOGIN_FIELDS.username}
              />

              <Controller
                control={control}
                rules={{
                  required: { value: true, message: "Password is required" },
                }}
                render={({
                  field: { onChange, value },
                  fieldState: { error },
                }) => (
                  <Input
                    placeholder="Password"
                    value={value}
                    onChangeText={onChange}
                    error={error}
                    isPassword
                  />
                )}
                name={LOGIN_FIELDS.password}
              />
            </View>

            <TextWithDuration
              text={loginErrorMessage}
              isError
              cbWhenComponentHide={clearErrorMessage}
              style={styles.errorText}
            />

            <Button
              title="Submit"
              onPress={handleSubmit(onSubmit)}
              containerStyle={styles.submitButton}
              isLoading={isLoading}
            />
          </View>
        </View>
      </KeyboardAvoidingView>
    </TouchableWithoutFeedback>
  );
};

export default LoginScreen;

const createStyles = ({ color, images }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
      padding: SPACING.TEN,
    },
    title: {
      fontSize: FONT_SIZES.TWENTY,
      textAlign: "center",
    },
    body: {
      marginTop: "auto",
      marginBottom: "auto",
    },
    logo: {
      marginBottom: SPACING.TWENTY_TWO,
      alignSelf: "center",
    },
    form: {
      backgroundColor: color.MESSAGE_BUTTONS_BACKGROUND,
      justifyContent: "center",
      padding: SPACING.S,
      borderRadius: SPACING.SIX,
    },
    formBody: {
      gap: SPACING.XS,
    },
    errorText: {
      marginTop: SPACING.L,
    },
    submitButton: {
      backgroundColor: color.BRAND_DEFAULT,
      marginTop: SPACING.XL,
    },
  });

  return { styles, images, color };
};
