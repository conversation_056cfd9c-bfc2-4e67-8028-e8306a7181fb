import xs from "xstream/index";
import sampleCombine from "xstream/extra/sampleCombine";
import {
  getProfileData,
  loginFailed,
  loginSuccess,
  tryLogin,
} from "../slices/authSlice";

export const bSignatureLogIn = (sources) => {
  const actionPayload$ = sources.ACTION.filter(
    (action) => action.type === tryLogin.type
  );

  const request$ = actionPayload$.compose(sampleCombine()).map(([action]) => {
    const { baseUrl, username, password } = action.payload || {};

    const queryParameters = new URLSearchParams();
    queryParameters.append("username", username);
    queryParameters.append("password", password);

    return {
      url: `${baseUrl}/authorize?${queryParameters.toString()}`,
      category: "bSignatureLogin",
      method: "GET",
      headers: {
        accept: "application/json",
        "Content-Type": "application/json",
      },
    };
  });

  const response$ = sources.HTTP.select("bSignatureLogin")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status === 200);

  const action1$ = xs
    .combine(response$)
    .map(([response]) => loginSuccess({ userId: response?.body?.userId }));

  const action2$ = response$
    .compose(sampleCombine(actionPayload$))
    .map(([response, actionPayload]) =>
      getProfileData({
        userId: response?.body?.userId,
        baseUrl: actionPayload?.payload?.baseUrl,
      })
    );

  return {
    ACTION: xs.merge(action1$, action2$),
    HTTP: request$,
  };
};

export const bSignatureLogInFailed = (sources) => {
  const response$ = sources.HTTP.select("bSignatureLogin")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status !== 200);

  const action$ = xs.combine(response$).map((arr) => loginFailed(arr));

  return {
    ACTION: action$,
  };
};
