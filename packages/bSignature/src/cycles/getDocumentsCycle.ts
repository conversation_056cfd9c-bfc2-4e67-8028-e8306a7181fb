import xs from "xstream/index";
import sampleCombine from "xstream/extra/sampleCombine";
import {
  getPendingDocuments,
  getPendingDocumentsFailed,
  getPendingDocumentsSuccess,
} from "../slices/pendingDocumentsSlice";
import {
  getCompletedDocuments,
  getCompletedDocumentsFailed,
  getCompletedDocumentsSuccess,
} from "../slices/completedDocumentsSlice";
import { storeDocuments } from "../slices/documentsSlice";
import {
  getTemporaryDocuments,
  getTemporaryDocumentsFailed,
  getTemporaryDocumentsSuccess,
} from "../slices/dashboardSlice";
import { DOCUMENT_APPROVAL_STATUS } from "../constants/DocumentApprovalStatus";

export const getDocumentsCycle = (sources) => {
  const state$ = sources.STATE;
  const userId$ = state$.map(
    (state) => state?.persist?.bSignatureAuthSlice?.userId
  );
  const baseUrl$ = state$.map(
    (state) => state?.persist?.bSignatureAuthSlice?.baseUrl
  );
  const actionPayload$ = sources.ACTION.filter(
    (action) =>
      action.type === getPendingDocuments.type ||
      action.type === getCompletedDocuments.type ||
      action.type === getTemporaryDocuments.type
  );

  const request$ = actionPayload$
    .compose(sampleCombine(userId$, baseUrl$))
    .map(([action, userId, baseUrl]) => {
      const { findWhat, approvalStatus } = action.payload || {};

      const queryParameters = new URLSearchParams();

      findWhat && queryParameters.append("findWhat", findWhat);
      Object.values(DOCUMENT_APPROVAL_STATUS).includes(approvalStatus) &&
        queryParameters.append("approvalStatusFlag", approvalStatus);

      return {
        url: `${baseUrl}/users/${userId}/documents?${queryParameters.toString()}`,
        category: "bSignatureGetDocuments",
        method: "GET",
        headers: {
          accept: "application/json",
          "Content-Type": "application/json",
        },
      };
    });

  const response$ = sources.HTTP.select("bSignatureGetDocuments")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status === 200);

  const action1$ = response$
    .compose(sampleCombine(actionPayload$))
    .map(([response, actionPayload]) => {
      const { approvalStatus, findWhat } = actionPayload?.payload || {};

      if (actionPayload.type === getTemporaryDocuments.type) {
        return getTemporaryDocumentsSuccess({
          responseBody: response?.body,
          approvalStatus,
          findWhat,
        });
      }

      if (actionPayload.type === getCompletedDocuments.type) {
        return getCompletedDocumentsSuccess({
          responseBody: response?.body,
          approvalStatus,
          findWhat,
        });
      }

      if (actionPayload.type === getPendingDocuments.type) {
        return getPendingDocumentsSuccess({
          responseBody: response?.body,
          approvalStatus,
          findWhat,
        });
      }
    });

  const action2$ = xs.combine(response$).map(([response]) =>
    storeDocuments({
      documents: response?.body,
    })
  );

  return {
    ACTION: xs.merge(action1$, action2$),
    HTTP: request$,
  };
};

export const getDocumentsCycleFailed = (sources) => {
  const actionPayload$ = sources.ACTION.filter(
    (action) =>
      action.type === getPendingDocuments.type ||
      action.type === getCompletedDocuments.type ||
      action.type === getTemporaryDocuments.type
  );

  const response$ = sources.HTTP.select("bSignatureGetDocuments")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status !== 200);

  const action$ = response$
    .compose(sampleCombine(actionPayload$))
    .map(([arr, actionPayload]) => {
      if (actionPayload.type === getTemporaryDocuments.type) {
        return getTemporaryDocumentsFailed(arr);
      }

      if (actionPayload.type === getCompletedDocuments.type) {
        return getCompletedDocumentsFailed(arr);
      }

      if (actionPayload.type === getPendingDocuments.type) {
        return getPendingDocumentsFailed(arr);
      }
    });

  return {
    ACTION: action$,
  };
};
