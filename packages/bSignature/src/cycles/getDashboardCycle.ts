import xs from "xstream/index";
import sampleCombine from "xstream/extra/sampleCombine";
import {
  getDashboard,
  getDashboardFailed,
  getDashboardSuccess,
} from "../slices/dashboardSlice";
import { storeDocuments } from "../slices/documentsSlice";

export const getDashboardCycle = (sources) => {
  const state$ = sources.STATE;
  // const userId$ = state$.map(
  //   (state) => state?.persist?.bSignatureAuthSlice?.userId
  // );
  // const baseUrl$ = state$.map(
  //   (state) => state?.persist?.bSignatureAuthSlice?.baseUrl
  // );

  const request$ = sources.ACTION.filter(
    (action) => action.type === getDashboard.type
  )
    .compose(sampleCombine())
    .map(([action]) => {
      const { baseUrl, userId } = action.payload || {};

      return {
        url: `${baseUrl}/users/${userId}/dashboard`,
        category: "bSignatureGetDashboard",
        method: "GET",
        headers: {
          accept: "application/json",
          "Content-Type": "application/json",
        },
      };
    });

  const response$ = sources.HTTP.select("bSignatureGetDashboard")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status === 200);

  const action1$ = xs
    .combine(response$)
    .map(([response]) => getDashboardSuccess({ responseBody: response?.body }));

  const action2$ = xs.combine(response$).map(([response]) =>
    storeDocuments({
      documents: response?.body?.documents,
    })
  );

  return {
    ACTION: xs.merge(action1$, action2$),
    HTTP: request$,
  };
};

export const getDashboardCycleFailed = (sources) => {
  const response$ = sources.HTTP.select("bSignatureGetDashboard")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status !== 200);

  const action$ = xs.combine(response$).map((arr) => getDashboardFailed(arr));

  return {
    ACTION: action$,
  };
};
