import xs from "xstream/index";
import sampleCombine from "xstream/extra/sampleCombine";
import {
  getProfileData,
  getProfileDataFailed,
  getProfileDataSuccess,
} from "../slices/authSlice";

export const bSignatureGetProfileData = (sources) => {
  const request$ = sources.ACTION.filter(
    (action) => action.type === getProfileData.type
  )
    .compose(sampleCombine())
    .map(([action]) => {
      const { userId, baseUrl } = action.payload || {};

      return {
        url: `${baseUrl}/users/${userId}/profile`,
        category: "bSignatureGetProfileData",
        method: "GET",
        headers: {
          accept: "application/json",
          "Content-Type": "application/json",
        },
      };
    });

  const response$ = sources.HTTP.select("bSignatureGetProfileData")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status === 200);

  const action$ = xs
    .combine(response$)
    .map(([response]) =>
      getProfileDataSuccess({ profileData: response?.body })
    );

  return {
    ACTION: action$,
    HTTP: request$,
  };
};

export const bSignatureGetProfileDataFailed = (sources) => {
  const response$ = sources.HTTP.select("bSignatureGetProfileData")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status !== 200);

  const action$ = xs.combine(response$).map((arr) => getProfileDataFailed(arr));

  return {
    ACTION: action$,
  };
};
