import xs from "xstream/index";
import sampleCombine from "xstream/extra/sampleCombine";
import { getCompletedDocuments } from "../slices/completedDocumentsSlice";
import {
  requestDocumentModification,
  requestDocumentModificationFailed,
  requestDocumentModificationSuccess,
} from "../slices/documentDetailsSlice";
import { DOCUMENT_APPROVAL_STATUS } from "../constants/DocumentApprovalStatus";

export const requestDocumentModificationCycle = (sources) => {
  const state$ = sources.STATE;
  const userId$ = state$.map(
    (state) => state?.persist?.bSignatureAuthSlice?.userId
  );
  const baseUrl$ = state$.map(
    (state) => state?.persist?.bSignatureAuthSlice?.baseUrl
  );
  const actionPayload$ = sources.ACTION.filter(
    (action) => action.type === requestDocumentModification.type
  );

  const request$ = actionPayload$
    .compose(sampleCombine(userId$, baseUrl$))
    .map(([action, userId, baseUrl]) => {
      const { documentId, comment } = action.payload || {};

      const queryParameters = new URLSearchParams();
      queryParameters.append("comments", comment);

      return {
        url: `${baseUrl}/users/${userId}/documents/${documentId}/request-modification?${queryParameters.toString()}`,
        category: "bSignatureRequestDocumentModification",
        method: "PATCH",
        headers: {
          accept: "application/json",
          "Content-Type": "application/json",
        },
      };
    });

  const response$ = sources.HTTP.select("bSignatureRequestDocumentModification")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status === 200 || response.status === 204);

  const action1$ = response$
    .compose(sampleCombine(actionPayload$))
    .map(([response, actionPayload]) =>
      requestDocumentModificationSuccess({
        documentId: actionPayload?.payload?.documentId,
      })
    );

  const action2$ = xs.combine(response$).map(([response]) =>
    getCompletedDocuments({
      approvalStatus: DOCUMENT_APPROVAL_STATUS.Pending,
    })
  );

  return {
    ACTION: xs.merge(action1$, action2$),
    HTTP: request$,
  };
};

export const requestDocumentModificationCycleFailed = (sources) => {
  const actionPayload$ = sources.ACTION.filter(
    (action) => action.type === requestDocumentModification.type
  );

  const response$ = sources.HTTP.select("bSignatureRequestDocumentModification")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status !== 200 && response.status !== 204);

  const action$ = response$
    .compose(sampleCombine(actionPayload$))
    .map((arr, actionPayload) =>
      requestDocumentModificationFailed({
        documentId: actionPayload?.payload?.documentId,
        errorMessage: "Something went wrong",
      })
    );

  return {
    ACTION: action$,
  };
};
