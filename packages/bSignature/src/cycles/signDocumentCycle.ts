import xs from "xstream/index";
import sampleCombine from "xstream/extra/sampleCombine";
import { getCompletedDocuments } from "../slices/completedDocumentsSlice";
import {
  signDocument,
  signDocumentFailed,
  signDocumentSuccess,
} from "../slices/documentDetailsSlice";

export const signDocumentCycle = (sources) => {
  const state$ = sources.STATE;
  const userId$ = state$.map(
    (state) => state?.persist?.bSignatureAuthSlice?.userId
  );
  const baseUrl$ = state$.map(
      (state) => state?.persist?.bSignatureAuthSlice?.baseUrl
  );
  const actionPayload$ = sources.ACTION.filter(
    (action) => action.type === signDocument.type
  );

  const request$ = actionPayload$
    .compose(sampleCombine(userId$, baseUrl$))
    .map(([action, userId, baseUrl]) => {
      const { documentId, signatureBase64, signatureName } =
        action.payload || {};

      return {
        url: `${baseUrl}/users/${userId}/documents/${documentId}/sign`,
        category: "bSignatureSignDocument",
        method: "PATCH",
        headers: {
          accept: "application/json",
          "Content-Type": "application/json",
        },
        send: {
          fileName: signatureName,
          base64Signature: signatureBase64,
        },
      };
    });

  const response$ = sources.HTTP.select("bSignatureSignDocument")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status === 200 || response.status === 204);

  const action1$ = response$
    .compose(sampleCombine(actionPayload$))
    .map(([response, actionPayload]) =>
      signDocumentSuccess({ documentId: actionPayload?.payload?.documentId })
    );

  const action2$ = xs.combine(response$).map(([response]) =>
    getCompletedDocuments({
      approvalStatus: undefined,
    })
  );

  return {
    ACTION: xs.merge(action1$, action2$),
    HTTP: request$,
  };
};

export const signDocumentCycleFailed = (sources) => {
  const actionPayload$ = sources.ACTION.filter(
    (action) => action.type === signDocument.type
  );

  const response$ = sources.HTTP.select("bSignatureSignDocument")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status !== 200 && response.status !== 204);

  const action$ = response$
    .compose(sampleCombine(actionPayload$))
    .map((arr, actionPayload) =>
      signDocumentFailed({
        documentId: actionPayload?.payload?.documentId,
        errorMessage: "Something went wrong",
      })
    );

  return {
    ACTION: action$,
  };
};
