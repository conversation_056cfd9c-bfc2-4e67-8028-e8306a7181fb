import React from "react";
import { SafeAreaView, StyleSheet } from "react-native";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { SCREEN_NAMES } from "../constants/screenNames";
import { useSelector } from "react-redux";

// Components
import { NavigationContainer } from "@react-navigation/native";
import TabsNavigation from "./TabsNavigaton";
import LoginHOC from "../containers/LoginHOC";
import { type Theme, useThemeAwareObject } from "b-ui-lib";

const Stack = createNativeStackNavigator();

const RootNavigation: React.FC = () => {
  const { userId } = useSelector((state) => state.persist.bSignatureAuthSlice);
  const { styles } = useThemeAwareObject(createStyles);

  return (
    <SafeAreaView style={styles.container}>
      <NavigationContainer>
        <Stack.Navigator
          initialRouteName={SCREEN_NAMES.login}
          screenOptions={{ headerShown: false }}
        >
          {!!userId ? (
            <>
              <Stack.Screen
                name={SCREEN_NAMES.tabsNavigation}
                component={TabsNavigation}
              />
            </>
          ) : (
            <Stack.Screen name={SCREEN_NAMES.login} component={LoginHOC} />
          )}
        </Stack.Navigator>
      </NavigationContainer>
    </SafeAreaView>
  );
};

const createStyles = ({ color, images }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.GREY2,
    },
  });

  return { styles, color, images };
};

export default RootNavigation;
