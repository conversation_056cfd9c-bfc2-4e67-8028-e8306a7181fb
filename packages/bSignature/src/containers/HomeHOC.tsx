import React, { useCallback, useState, useMemo, useEffect } from "react";
import { StyleSheet } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import {
  NavigationProp,
  ParamListBase,
  useFocusEffect,
  useNavigation,
} from "@react-navigation/native";
import {
  clearHasSearchFlag,
  getDashboard,
  getTemporaryDocuments,
} from "../slices/dashboardSlice";
import { SCREEN_NAMES } from "../constants/screenNames";
import { mapDocumentForView } from "../helpers/mappers/mapDocumentForView";
import { Document } from "../types/document";

// Components
import HomeScreen from "../components/home/<USER>";
import { Theme, useThemeAwareObject } from "b-ui-lib";

const HomeHOC: React.FC = () => {
  const [searchInputValue, setSearchInputValue] = useState<string>("");

  const { documentsById } = useSelector(
    (state) => state.root.bSignatureDocumentsSlice
  );

  const {
    latestDocumentsAllIds,
    pendingDocumentsCount,
    completedDocumentsCount,
    latestDocumentsIsLoading,
    latestDocumentsErrorMessage,
    hasSearch,
    temporaryDocumentsAllIds,
    temporaryDocumentsIsLoading,
    temporaryDocumentsErrorMessage,
  } = useSelector((state) => state.root.bSignatureDashboardSlice);

  const { baseUrl, userId } = useSelector(
    (state) => state.persist.bSignatureAuthSlice
  );

  const { color } = useThemeAwareObject(createStyles);
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const dispatch = useDispatch();

  const getDashboardAction = (payload) => dispatch(getDashboard(payload));

  const clearHasSearchFlagAction = () => dispatch(clearHasSearchFlag());

  const getTemporaryDocumentsAction = (inputText: string) => {
    if (inputText === "" || !inputText) {
      return;
    }

    dispatch(
      getTemporaryDocuments({
        findWhat: inputText,
        approvalStatus: null,
      })
    );
  };

  useFocusEffect(
    useCallback(() => {
      if (baseUrl && userId) {
        getDashboardAction({ userId, baseUrl });
      }
    }, [])
  );

  useEffect(() => {
    if (!searchInputValue && hasSearch) {
      clearHasSearchFlagAction();
    }
  }, [searchInputValue]);

  const isSearching = hasSearch && searchInputValue;

  const mappedDocuments = useMemo((): Document[] => {
    if (isSearching) {
      return temporaryDocumentsAllIds?.map((documentId: string) =>
        mapDocumentForView(documentsById[documentId], color)
      );
    }

    return latestDocumentsAllIds?.map((documentId: string) =>
      mapDocumentForView(documentsById[documentId], color)
    );
  }, [
    documentsById,
    latestDocumentsAllIds,
    temporaryDocumentsAllIds,
    isSearching,
  ]);

  const handleSearchInputChange = (textValue: string) =>
    setSearchInputValue(textValue);

  const handleSearchInputClear = () => {
    setSearchInputValue("");
    clearHasSearchFlagAction();
  };

  const handlePressDocument = (selectedDocumentId: string) =>
    navigation.navigate(SCREEN_NAMES.documentDetails, { selectedDocumentId });

  return (
    <HomeScreen
      searchInputValue={searchInputValue}
      handleSearchInputChange={handleSearchInputChange}
      handleSearchInputClear={handleSearchInputClear}
      pendingDocumentsCount={pendingDocumentsCount}
      completedDocumentsCount={completedDocumentsCount}
      documents={mappedDocuments}
      isLoading={latestDocumentsIsLoading || temporaryDocumentsIsLoading}
      errorMessage={
        isSearching
          ? temporaryDocumentsErrorMessage
          : latestDocumentsErrorMessage
      }
      handleRefreshList={getDashboardAction}
      handlePressDocument={handlePressDocument}
      handleDebounceGetDocuments={getTemporaryDocumentsAction}
    />
  );
};

export default HomeHOC;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({});
  return { styles, color };
};
