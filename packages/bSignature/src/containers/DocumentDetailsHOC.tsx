import React, { useEffect, useMemo, useState } from "react";
import { StyleSheet } from "react-native";
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
  useRoute,
} from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import { mapDocumentForView } from "../helpers/mappers/mapDocumentForView";
import {
  clearSignRequestDocumentDetailsSuccessErrorMessages,
  signDocument,
} from "../slices/documentDetailsSlice";
import { DocumentForView } from "../types/documentForView";
import { downloadSingleAttachment } from "../../../bcomponents/downloadHelpers/downloadAttachmentHelper";
import { SCREEN_NAMES } from "../constants/screenNames";
import ImageResizer from "@bam.tech/react-native-image-resizer";
import RNFS from "react-native-fs";

// Components
import { Theme, useThemeAwareObject } from "b-ui-lib";
import DocumentDetailsScreen from "../components/DocumentDetails/DocumentDetailsScreen";

export type TopBarInfo = {
  title: string;
  date: string;
  backgroundColor: string;
};

const DocumentDetailsHOC: React.FC = () => {
  const [topBarNotificationInfo, setTopBarNotificationInfo] =
    useState<TopBarInfo | null>(null);
  const [isSignModalOpen, setIsSignModalOpen] = useState<boolean>(false);
  const route = useRoute();
  const { selectedDocumentId, shouldNotClearSuccessErrorMessages } =
    route.params as {
      selectedDocumentId: string;
      shouldNotClearSuccessErrorMessages: string;
    };
  const dispatch = useDispatch();
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const { color } = useThemeAwareObject(createStyles);

  const { baseUrl } = useSelector((state) => state.persist.bSignatureAuthSlice);

  const { documentsById } = useSelector(
    (state) => state.root.bSignatureDocumentsSlice
  );

  const selectedDocumentActions = useSelector(
    (state) =>
      state.root?.bSignatureDocumentDetailsSlice?.documentActionsById?.[
        selectedDocumentId
      ]
  );

  const {
    signDocumentSuccessMessage,
    signDocumentErrorMessage,
    requestDocumentModificationSuccessMessage,
    requestDocumentModificationErrorMessage,
  } = selectedDocumentActions || {};

  const clearSignRequestDocumentDetailsSuccessErrorMessagesAction = () =>
    dispatch(
      clearSignRequestDocumentDetailsSuccessErrorMessages({
        documentId: selectedDocumentId,
      })
    );

  const signDocumentAction = async (signature: string) => {
    const resizedImage = await ImageResizer.createResizedImage(
      signature,
      600,
      300,
      "PNG",
      90,
      0,
      undefined,
      true
    );

    const resizedBase64 = await RNFS.readFile(resizedImage.uri, "base64");

    // Delete temp file.
    await RNFS.unlink(resizedImage.uri).catch(() => {});

    dispatch(
      signDocument({
        documentId: selectedDocumentId,
        signatureBase64: resizedBase64,
        signatureName: `signature-${new Date().toLocaleDateString()}.png`,
      })
    );
  };

  const mappedDocument = useMemo((): DocumentForView => {
    return mapDocumentForView(documentsById?.[selectedDocumentId], color);
  }, [documentsById, selectedDocumentId]);

  // Clear success/error messages
  useEffect(() => {
    clearSignRequestDocumentDetailsSuccessErrorMessagesAction();
  }, []);

  // Close modal after sign document
  useEffect(() => {
    if (
      isSignModalOpen &&
      (signDocumentSuccessMessage || signDocumentErrorMessage)
    ) {
      setIsSignModalOpen(false);
    }
  }, [signDocumentSuccessMessage, signDocumentErrorMessage]);

  // Set TobBarNotificationInfo
  useEffect(() => {
    if (
      !signDocumentSuccessMessage &&
      !signDocumentErrorMessage &&
      !requestDocumentModificationSuccessMessage &&
      !requestDocumentModificationErrorMessage
    ) {
      return setTopBarNotificationInfo(null);
    }

    if (signDocumentSuccessMessage) {
      return setTopBarNotificationInfo({
        title: signDocumentSuccessMessage,
        date: mappedDocument.signedDate,
        backgroundColor: color.MESSAGE_ITEM__ARROW_DOWN,
      });
    }

    if (requestDocumentModificationSuccessMessage) {
      return setTopBarNotificationInfo({
        title: requestDocumentModificationSuccessMessage,
        date: new Date().toISOString(),
        backgroundColor: color.BRAND_DEFAULT,
      });
    }

    if (signDocumentErrorMessage || requestDocumentModificationErrorMessage) {
      return setTopBarNotificationInfo({
        title:
          signDocumentErrorMessage || requestDocumentModificationErrorMessage,
        date: "",
        backgroundColor: color.ERROR,
      });
    }
  }, [
    signDocumentSuccessMessage,
    signDocumentErrorMessage,
    requestDocumentModificationSuccessMessage,
    requestDocumentModificationErrorMessage,
  ]);

  const PDF_SOURCE_URL = `${baseUrl}/documents/${mappedDocument?.id}/preview`;

  const handleDownloadPdf = async () => {
    await downloadSingleAttachment(
      "",
      "",
      mappedDocument.name,
      () => {},
      PDF_SOURCE_URL
    );
  };

  const handleRequestRevision = () => {
    navigation.navigate(SCREEN_NAMES.requestDocumentModification, {
      selectedDocumentId,
    });
  };

  const handleOpenSignModal = () => setIsSignModalOpen(true);
  const handleCloseSignModal = () => setIsSignModalOpen(false);

  return (
    <DocumentDetailsScreen
      navigation={navigation}
      document={mappedDocument}
      handleSignDocument={signDocumentAction}
      topBarNotificationInfo={topBarNotificationInfo}
      handleDownloadPdf={handleDownloadPdf}
      pdfSourceUrl={PDF_SOURCE_URL}
      handleRequestRevision={handleRequestRevision}
      isSignModalOpen={isSignModalOpen}
      handleOpenSignModal={handleOpenSignModal}
      handleCloseSignModal={handleCloseSignModal}
    />
  );
};

export default DocumentDetailsHOC;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({});
  return { styles, color };
};
