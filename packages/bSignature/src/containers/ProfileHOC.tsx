import React from "react";
import { useDispatch, useSelector } from "react-redux";

// Components
import ProfileScreen from "../components/profile/ProfileScreen";
import { logout } from "../slices/authSlice";
import { ProfileData } from "../types/profileData";

const ProfileHOC: React.FC = () => {
  const dispatch = useDispatch();
  const profileData: ProfileData = useSelector(
    (state) => state.persist.bSignatureAuthSlice?.profileData
  );

  const logoutAction = () => dispatch(logout());

  return (
    <ProfileScreen
      firstname={profileData.firstname}
      lastname={profileData.lastname}
      roleName={profileData.roleName}
      vesselName={profileData.vesselName}
      companyName={profileData.companyName}
      logout={logoutAction}
    />
  );
};

export default ProfileHOC;
