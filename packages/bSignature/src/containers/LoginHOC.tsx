import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { clearErrorMessage, tryLogin } from "../slices/authSlice";

// Components
import LoginScreen from "../components/login/LoginScreen";

export type FormData = {
  baseUrl: string;
  username: string;
  password: string;
};

const LoginHOC: React.FC = () => {
  const dispatch = useDispatch();
  const { baseUrl, username, password, loginIsLoading, loginErrorMessage } =
    useSelector((state) => state.persist.bSignatureAuthSlice);

  const onSubmit = (formData: FormData) => {
    const { baseUrl, username, password } = formData || {};

    dispatch(tryLogin({ baseUrl, username, password }));
  };

  const clearErrorMessageAction = () => dispatch(clearErrorMessage());

  return (
    <LoginScreen
      baseUrl={baseUrl}
      username={username}
      password={password}
      loginErrorMessage={loginErrorMessage}
      isLoading={loginIsLoading}
      onSubmit={onSubmit}
      clearErrorMessage={clearErrorMessageAction}
    />
  );
};

export default LoginHOC;
