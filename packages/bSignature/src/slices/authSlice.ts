import { createSlice } from "@reduxjs/toolkit";
import { ProfileDataDTO } from "../types/DTOs/profileDataDTO";
import { mapProfileData } from "../helpers/mappers/mapProfileData";
import { ProfileData } from "../types/profileData";

type State = {
  baseUrl: string;
  username: string;
  password?: string;

  userId: string;
  loginIsLoading: boolean;
  loginErrorMessage?: string;

  profileData?: ProfileData;
  profileDataIsLoading?: boolean;
  profileDataErrorMessage?: string;
};

const initialState: State = {
  baseUrl: "",
  username: "",
  password: "",
  userId: "",
  loginIsLoading: false,
  loginErrorMessage: "",
  profileData: null,
  profileDataIsLoading: false,
  profileDataErrorMessage: "",
};

const authSlice = createSlice({
  name: "bSignatureAuth",
  initialState,
  reducers: {
    tryLogin: (state, action) => {
      const { baseUrl, username, password } = action.payload || {};

      state.baseUrl = baseUrl;
      state.username = username;
      state.password = password;
      state.loginIsLoading = true;
      state.loginErrorMessage = "";
    },
    loginSuccess: (state, action) => {
      const { userId } = action.payload;

      state.userId = userId;
      state.loginIsLoading = false;
    },
    loginFailed: (state, action) => {
      state.loginIsLoading = false;
      state.loginErrorMessage =
        action?.payload?.[0]?.response?.body?.["odata.error"]?.message?.value ||
        "Invalid username or password";
    },
    logout: (state) => {
      state.userId = "";
    },
    clearErrorMessage: (state) => {
      state.loginErrorMessage = "";
    },
    getProfileData: (state, action) => {
      state.profileDataIsLoading = true;
      state.profileDataErrorMessage = "";
    },
    getProfileDataSuccess: (state, action) => {
      const { profileData }: { profileData: ProfileDataDTO } = action.payload;

      state.profileDataIsLoading = false;
      state.profileData = mapProfileData(profileData);
    },
    getProfileDataFailed: (state, action) => {
      state.profileDataIsLoading = false;
      state.profileDataErrorMessage =
        action?.payload?.[0]?.response?.body?.["odata.error"]?.message?.value ||
        "Something went wrong";
    },
  },
});

// Export the action creators for dispatching
export const {
  tryLogin,
  loginSuccess,
  loginFailed,
  logout,
  clearErrorMessage,
  getProfileData,
  getProfileDataSuccess,
  getProfileDataFailed,
} = authSlice.actions;

export default authSlice.reducer;
