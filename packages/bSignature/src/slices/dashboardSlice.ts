import { createSlice } from "@reduxjs/toolkit";
import { LatestDocumentsDTO } from "../types/DTOs/latestDocumentsDTO";
import { DocumentDTO } from "../types/DTOs/documentDTO";

type State = {
  latestDocumentsAllIds: string[];
  pendingDocumentsCount: string;
  completedDocumentsCount: string;
  latestDocumentsIsLoading: boolean;
  latestDocumentsErrorMessage: string;

  hasSearch: boolean;
  temporaryDocumentsAllIds: string[];
  temporaryDocumentsIsLoading: boolean;
  temporaryDocumentsErrorMessage: string;
};

const initialState: State = {
  latestDocumentsAllIds: [],
  pendingDocumentsCount: "",
  completedDocumentsCount: "",
  latestDocumentsIsLoading: false,
  latestDocumentsErrorMessage: "",

  hasSearch: false,
  temporaryDocumentsAllIds: [],
  temporaryDocumentsIsLoading: false,
  temporaryDocumentsErrorMessage: "",
};

const dashboardSlice = createSlice({
  name: "bSignatureDashboardSlice",
  initialState,
  reducers: {
    getDashboard: (state, payload) => {
      state.latestDocumentsIsLoading = true;
      state.latestDocumentsErrorMessage = "";
    },
    getDashboardSuccess: (state, action) => {
      const response: LatestDocumentsDTO = action.payload?.responseBody;
      const { documents, pendingCount, completedCount } = response || {};

      const newDocumentsAllIds = documents.map((doc) => doc.id.toString());

      state.latestDocumentsIsLoading = false;
      state.latestDocumentsAllIds = newDocumentsAllIds;
      state.pendingDocumentsCount = pendingCount?.toString();
      state.completedDocumentsCount = completedCount?.toString();
    },
    getDashboardFailed: (state, action) => {
      state.latestDocumentsIsLoading = false;
      state.latestDocumentsErrorMessage =
        action?.payload?.[0]?.response?.body?.["odata.error"]?.message?.value ||
        "Something went wrong while showing dashboard information";
    },
    getTemporaryDocuments: (state, action) => {
      state.temporaryDocumentsIsLoading = true;
      state.temporaryDocumentsErrorMessage = "";
    },
    getTemporaryDocumentsSuccess: (state, action) => {
      const {
        responseBody,
      }: {
        responseBody: DocumentDTO[];
      } = action.payload;

      state.temporaryDocumentsIsLoading = false;
      state.hasSearch = true;
      state.temporaryDocumentsAllIds = responseBody.map((doc) =>
        doc?.id.toString()
      );
    },
    getTemporaryDocumentsFailed: (state, action) => {
      state.temporaryDocumentsIsLoading = false;
      state.hasSearch = true;
      state.temporaryDocumentsErrorMessage =
        action?.payload?.[0]?.response?.body?.["odata.error"]?.message?.value ||
        "Something went wrong while searching documents";
    },
    clearHasSearchFlag: (state) => {
      state.hasSearch = false;
    },
  },
});

// Export the action creators for dispatching
export const {
  getDashboard,
  getDashboardSuccess,
  getDashboardFailed,
  getTemporaryDocuments,
  getTemporaryDocumentsSuccess,
  getTemporaryDocumentsFailed,
  clearHasSearchFlag,
} = dashboardSlice.actions;

export default dashboardSlice.reducer;
