import React, { useEffect, useMemo, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {ActivityIndicator, View, StyleSheet, Platform} from "react-native";
import { CustomText, useThemeAwareObject, Theme } from "b-ui-lib";
import BottomSheet from "@gorhom/bottom-sheet";
import {
  performDeleteAction,
  performMarkAsReadUnreadAction,
  fetchGridMessages,
  flagMessages,
  performUnDeleteAction,
  clearCopyOrMoveMessageFolderError,
  clearCopyOrMoveMessageFolderSuccess,
  clearAllErrors,
  clearOffset,
} from "../slices/gridMessageSlice";
import {
  setBottomSheetMenuState,
  longTapToSelectEmail,
  tapToSelectAdditionalEmail,
  deselectEmail,
  cancelMultiSelection,
  selectAllEmails,
  clearAllSelections,
  scrollToHideHeader,
  scrollToRevealHeader,
} from "../slices/generalSlice";
import { getFolders } from "../slices/gridMessageSlice";
import {
  getUserEmailAdresses,
  getUserUniboxes,
  getUserUniboxesInfos,
} from "../slices/usersMailDomainSlice";
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native";
import { SCREEN_NAMES } from "../constants/screenNames";
import { EMAIL_CASES } from "../constants/emailCases";
import { addSearchSuggestion } from "../slices/searchSuggestionsSlice";
import { fetchGridCases } from "../slices/casesSlice";
import { TEST_IDS } from "../constants/testIds";
import { FLAGGED_API_VALUES } from "../constants/apiValues";
import { SEARCH_ENTITIES } from "../constants/searchEntities";
import {
  clearSearchFields,
  setIsQuickSearchEnabled,
  setSearchFilters,
} from "../slices/searchFiltersSlice";
import { denormalizeGriMessagesSections } from "../helpers/denormalizeGriMessagesSections";
import { fetchParticipantUsers } from "../slices/generalNotificationsSlice";

// Components
import InboxScreen from "../components/inbox/InboxScreen";
import Toast from "react-native-toast-message";
import InboxSearchHeader from "../navigation/headers/InboxSearchHeader";
import SearchBarAnimationHOC from "./SearchBarAnimationHOC";
import { validateSearchText } from "../helpers/validateSearchText";

export const BOTTOM_SHEET_MENU_STATES = {
  default: "default",
  more: "more",
};

export type BottomSheetMenuState =
  (typeof BOTTOM_SHEET_MENU_STATES)[keyof typeof BOTTOM_SHEET_MENU_STATES];

export const INITIAL_PAGE_SIZE = 100;

const DEFAULT_HEADER_HEIGHT = Platform.OS === 'ios' ? 94 : 107;
const DEFAULT_HEADER_HEIGHT_WITH_ERROR_MESSAGE = Platform.OS === 'ios' ? 111 : 124;

const InboxHOC: React.FC = () => {
  // SELECTORS START

  const {
    gridMessages,
    folders,
    selectedFolderId,
    gridMessagesLoading,
    gridMessagesError,
    gridMessageCount,
    gridMessagesLoadingMore,
    copyOrMoveMessageFolderError,
    copyOrMoveMessageFolderSuccess,
    offset,
  } = useSelector((state) => state.persist.gridMessageSlice);

  const {
    searchFilters,
    appliedSearchFiltersCount,
    isQuickSearchEnabled,
    filteredIds,
  } = useSelector(
    (state) => state.root.bTeamSearchFiltersSlice?.[SEARCH_ENTITIES.messages]
  );

  const {
    bottomSheetMenuState,
    isMultiSelectActive,
    selectedMessageIds,
    isHeaderVisible,
  } = useSelector((state) => state.root.bTeamGeneralSlice);

  const { searchSuggestions } = useSelector(
    (state) =>
      state.persist.bTeamSearchSuggestionsSlice?.[SEARCH_ENTITIES.messages]
  );

  // SELECTORS END

  const [validationErrorMessage, setValidationErrorMessage] = useState<
    string | null
  >(null);
  const { styles, color } = useThemeAwareObject(createStyles);
  const dispatch = useDispatch();
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const bottomSheetRef = useRef<BottomSheet>(null);

  // If the user applies a search filter, we want to display emails from a different list
  const emailIds =
    appliedSearchFiltersCount > 0
      ? filteredIds
      : folders.byId[selectedFolderId]?.emailIds;

  const sectionListData = denormalizeGriMessagesSections(
    gridMessages,
    emailIds
  );

  const isDeletedFolder = folders.allIds.find(
    (id) => folders.byId[id].name === "Deleted Items"
  );

  const selectedMessages = useMemo(() => {
    const allMessages = sectionListData.flatMap((section) => section.data);
    return allMessages.filter((message) =>
      selectedMessageIds?.includes(message.id)
    );
  }, [sectionListData, selectedMessageIds]);

  const hasSelectedReadMessages = useMemo(() => {
    return selectedMessages.some((message) => message.isViewed);
  }, [selectedMessages]);

  const hasSelectedUnReadMessages = useMemo(() => {
    return selectedMessages.some((message) => !message.isViewed);
  }, [selectedMessages]);

  const hasSelectedOnlyUnFlaggedMessages = useMemo(() => {
    return selectedMessages.every((message) => !message.isFlagged);
  }, [selectedMessages]);

  // ACTION DISPATCHERS START

  const fetchGridMessagesAction = (payload) =>
    dispatch(fetchGridMessages(payload));

  const fetchParticipantUsersAction = () => {
    dispatch(fetchParticipantUsers());
  };

  const handleSetBottomSheetMenuStateAction = (
    bottomSheetState: BottomSheetMenuState
  ) => {
    dispatch(setBottomSheetMenuState(bottomSheetState));
  };

  const handleLongTapToSelectEmailAction = (id: string) => {
    dispatch(longTapToSelectEmail(id));
  };

  const handleTapToSelectAdditionalEmailAction = (id: string) => {
    dispatch(tapToSelectAdditionalEmail(id));
  };

  const handleDeselectEmailAction = (id: string) => {
    dispatch(deselectEmail(id));
  };

  const handleCancelMultiSelectionAction = () => {
    dispatch(cancelMultiSelection());
  };

  const handleDeleteSelectedEmailsAction = (payload: {
    ids: string[];
    shouldCloseMultiSelect?: boolean;
  }) => {
    dispatch(performDeleteAction(payload));
  };

  const handleUnDeleteSelectedEmailsAction = (payload: {
    ids: string[];
    shouldCloseMultiSelect?: boolean;
  }) => {
    dispatch(performUnDeleteAction(payload));
  };

  const handleMarkAsReadUnreadAction = () => {
    let mode;
    if (selectedMessages.length === 1) {
      // For a single message, check its status directly.
      mode = selectedMessages[0].isViewed ? 7 : 6;
    } else {
      // For multiple messages, if every selected message is unread, mark as read.
      // Otherwise (i.e. if any message is already read), mark as unread.
      mode = selectedMessages.every((message) => !message.isViewed) ? 6 : 7;
    }

    dispatch(
      performMarkAsReadUnreadAction({
        ids: selectedMessageIds,
        mode,
        shouldCloseMultiSelect: true,
      })
    );
  };

  const clearInboxSearchFieldsAction = () => {
    dispatch(clearSearchFields({ entityType: SEARCH_ENTITIES.messages }));
  };

  const setInboxSearchFieldsAction = (fields: {}) => {
    dispatch(
      setSearchFilters({
        entityType: SEARCH_ENTITIES.messages,
        filters: fields,
      })
    );
  };

  const addSearchSuggestionAction = (suggestion: string) =>
    dispatch(
      addSearchSuggestion({
        entityType: SEARCH_ENTITIES.messages,
        suggestion,
      })
    );
  const handleSelectAllEmailsAction = () => {
    dispatch(selectAllEmails(emailIds));
  };

  const handleClearAllSelectionsAction = () => {
    dispatch(clearAllSelections());
  };

  const handleScrollToHideHeaderAction = () => {
    dispatch(scrollToHideHeader());
  };

  const handleScrollToRevealHeaderAction = () => {
    dispatch(scrollToRevealHeader());
  };

  const handleFlagMessageAction = (id?: string) => {
    // Single message selected case
    if (id) {
      // For a single message, check its status directly.
      const value = gridMessages.byId[id]?.isFlagged
        ? FLAGGED_API_VALUES.unFlag
        : FLAGGED_API_VALUES.flag;
      return dispatch(
        flagMessages({ ids: [id], value, shouldCloseMultiSelect: true })
      );
    }

    // Multiselect message case
    if (selectedMessageIds?.length > 0) {
      // For multiple messages, if every selected message is unFlagged, then flag.
      // Otherwise, (i.e. if any message is already flagged), then unFlagged.
      const value = hasSelectedOnlyUnFlaggedMessages
        ? FLAGGED_API_VALUES.flag
        : FLAGGED_API_VALUES.unFlag;
      dispatch(
        flagMessages({
          ids: selectedMessageIds,
          value,
          shouldCloseMultiSelect: true,
        })
      );
    }
  };

  const clearCopyOrMoveMessageFolderErrorAction = () => {
    dispatch(clearCopyOrMoveMessageFolderError());
  };

  const clearCopyOrMoveMessageFolderSuccessAction = () => {
    dispatch(clearCopyOrMoveMessageFolderSuccess());
  };

  const getFoldersAction = () => {
    dispatch(getFolders());
  };

  const getUserUniboxesAction = () => {
    dispatch(getUserUniboxes());
  };
  const getUserUniboxesInfosAction = () => {
    dispatch(getUserUniboxesInfos());
  };

  const getUserEmailAdressesAction = () => {
    dispatch(getUserEmailAdresses());
  };

  const fetchGridCasesAction = () => {
    dispatch(fetchGridCases(0));
  };

  const clearOffsetAction = () => {
    dispatch(clearOffset());
  };

  const setIsQuickSearchEnabledAction = (isEnabled: boolean) => {
    dispatch(
      setIsQuickSearchEnabled({
        entityType: SEARCH_ENTITIES.messages,
        isEnabled,
      })
    );
  };

  const clearAllErrorsAction = () => {
    dispatch(clearAllErrors());
  };

  // ACTION DISPATCHERS END

  // EFFECTS START

  useEffect(() => {
    // Clear any existing errors when component mounts
    setTimeout(() => clearAllErrorsAction(), 1);
    setTimeout(() => getFoldersAction(), 2);
    setTimeout(() => getUserUniboxesAction(), 3);
    setTimeout(() => getUserUniboxesInfosAction(), 4);
    setTimeout(() => getUserEmailAdressesAction(), 5);
    setTimeout(() => fetchParticipantUsersAction(), 6);
    setTimeout(() => fetchGridCasesAction(), 7);
    setTimeout(() => clearOffsetAction(), 8);
  }, []);

  useEffect(() => {
    if (selectedFolderId) {
      fetchGridMessagesAction();
    }
  }, [selectedFolderId]);

  // Reset on folder change
  useEffect(() => {
    if (selectedFolderId) {
      clearInboxSearchFieldsAction();
    }
  }, [selectedFolderId]);

  useEffect(() => {
    if (!copyOrMoveMessageFolderSuccess && !copyOrMoveMessageFolderError)
      return;

    if (copyOrMoveMessageFolderSuccess || copyOrMoveMessageFolderError) {
      Toast.show({
        type: copyOrMoveMessageFolderSuccess ? "success" : "error",
        text1: copyOrMoveMessageFolderSuccess
          ? copyOrMoveMessageFolderSuccess
          : copyOrMoveMessageFolderError,
        onPress() {
          Toast.hide();
        },
        onHide: () => {
          copyOrMoveMessageFolderSuccess
            ? clearCopyOrMoveMessageFolderSuccessAction()
            : clearCopyOrMoveMessageFolderErrorAction();
        },
        position: "bottom",
      });
    }
  }, [copyOrMoveMessageFolderSuccess, copyOrMoveMessageFolderError]);

  useEffect(() => {
    const validationError = validateSearchText(searchFilters.searchText);

    if (validationError !== validationErrorMessage) {
      setValidationErrorMessage(validationError);
    }
  }, [searchFilters.searchText]);

  // EFFECTS END

  const handleMoveSelectedEmailsToFolder = () => {
    navigation.navigate(SCREEN_NAMES.folders, {
      emailCase: EMAIL_CASES.move,
      isMultiSelect: true,
      messageId: null,
    });
  };

  const handleCopySelectedEmailsToFolder = () => {
    navigation.navigate(SCREEN_NAMES.folders, {
      emailCase: EMAIL_CASES.copy,
      isMultiSelect: true,
      messageId: null,
    });
  };

  const handleFindRelatedMessages = () => {
    // Only navigate if exactly one message is selected
    if (selectedMessageIds?.length === 1) {
      navigation.navigate(SCREEN_NAMES.relatedMessages, {
        UMS_Guid: selectedMessageIds?.[0],
      });
    }
  };

  const handleTapMessage = (messageId: string) => {
    navigation.navigate(SCREEN_NAMES.message, {
      UMS_Guid: messageId,
      folderId: selectedFolderId,
    });
  };

  // Infinite scroll load more function
  const loadMoreMessages = () => {
    if (
      !gridMessagesLoadingMore &&
      emailIds?.length > 0 &&
      gridMessageCount > 0 &&
      emailIds?.length < gridMessageCount
    ) {
      const newOffset = offset + INITIAL_PAGE_SIZE;

      fetchGridMessagesAction({ offset: newOffset });
    }
  };

  const BOTTOM_SHEET_DEFAULT_OPTIONS = [
    {
      title:
        !hasSelectedReadMessages && hasSelectedUnReadMessages
          ? "Mark as read"
          : "Mark as unread",
      onPress: () => handleMarkAsReadUnreadAction(),
      icon: "",
      testID: TEST_IDS.inboxBottomSheetMarkAsReadButton,
      isDisabled: selectedMessageIds?.length <= 0,
    },
    {
      title: isDeletedFolder === selectedFolderId ? "Restore" : "Delete",
      onPress: () =>
        isDeletedFolder === selectedFolderId
          ? handleUnDeleteSelectedEmailsAction({
              ids: selectedMessageIds,
              shouldCloseMultiSelect: true,
            })
          : handleDeleteSelectedEmailsAction({
              ids: selectedMessageIds,
              shouldCloseMultiSelect: true,
            }),
      icon: "",
      testID: TEST_IDS.inboxBottomSheetDeleteButton,
      isDisabled: selectedMessageIds?.length <= 0,
    },
    {
      title: "More",
      onPress: () => {
        bottomSheetRef.current?.snapToIndex(1);
        setBottomSheetMenuState(BOTTOM_SHEET_MENU_STATES.more);
      },
      icon: "",
      testID: TEST_IDS.inboxBottomSheetMoreButton,
      isDisabled: false,
    },
  ];

  const BOTTOM_SHEET_MORE_OPTIONS = [
    {
      title:
        !hasSelectedReadMessages && hasSelectedUnReadMessages
          ? "Mark as read"
          : "Mark as unread",
      onPress: () => {
        handleMarkAsReadUnreadAction();
      },
      icon: "mail",
      testID: TEST_IDS.inboxBottomSheetMarkAsReadMoreButton,
      isDisabled: selectedMessageIds?.length <= 0,
    },
    {
      title: "Move to folder",
      onPress: () => handleMoveSelectedEmailsToFolder(),
      icon: "folder",
      testID: TEST_IDS.inboxBottomSheetMoveToFolderButton,
      isDisabled: selectedMessageIds?.length <= 0,
    },
    {
      title: "Copy to folder",
      onPress: () => handleCopySelectedEmailsToFolder(),
      icon: "folder-plus",
      testID: TEST_IDS.inboxBottomSheetCopyToFolderButton,
      isDisabled: selectedMessageIds?.length <= 0,
    },
    {
      title: "Link to a case",
      onPress: () => {},
      icon: "briefcase",
      testID: TEST_IDS.inboxBottomSheetLinkToCaseButton,
      isDisabled: selectedMessageIds?.length <= 0,
    },
    {
      title: hasSelectedOnlyUnFlaggedMessages ? "Flag" : "Unflag",
      onPress: () => {
        handleFlagMessageAction();
      },
      icon: "flag",
      testID: TEST_IDS.inboxBottomSheetFlagButton,
      isDisabled: selectedMessageIds?.length <= 0,
    },
    {
      title: "Find Related Messages",
      onPress: () => {
        if (selectedMessageIds?.length === 1) {
          handleFindRelatedMessages();
        } else {
          // Show toast message when no message or multiple messages are selected
          Toast.show({
            type: "info",
            text1: "Please select exactly one message",
            text2:
              selectedMessageIds?.length === 0
                ? "You need to select a message first"
                : "You can only find related messages for one message at a time",
            position: "top",
          });
        }
      },
      icon: "search",
      // Disable the option when no message or multiple messages are selected
      isDisabled: selectedMessageIds?.length !== 1,
      testID: TEST_IDS.inboxBottomSheetFindRelatedButton,
    },
    {
      title: "Select All",
      onPress: () => handleSelectAllEmailsAction(),
      icon: "",
      testID: TEST_IDS.inboxBottomSheetSelectAllButton,
      isDisabled: emailIds?.length === selectedMessageIds?.length,
    },
    {
      title: `Unselect All (${selectedMessageIds?.length})`,
      onPress: () => handleClearAllSelectionsAction(),
      icon: "",
      testID: TEST_IDS.inboxBottomSheetUnSelectAllButton,
      isDisabled: selectedMessageIds?.length <= 0,
    },
  ];

  return (
    <SearchBarAnimationHOC
      headerHeight={
        validationErrorMessage
          ? DEFAULT_HEADER_HEIGHT_WITH_ERROR_MESSAGE
          : DEFAULT_HEADER_HEIGHT
      }
      searchBar={
        <InboxSearchHeader
          navigation={navigation}
          inboxSearchFields={searchFilters}
          appliedSearchFiltersCount={appliedSearchFiltersCount}
          clearInboxSearchFields={clearInboxSearchFieldsAction}
          setInboxSearchFields={setInboxSearchFieldsAction}
          fetchGridMessages={fetchGridMessagesAction}
          searchSuggestions={searchSuggestions}
          addSearchSuggestion={addSearchSuggestionAction}
          searchInputRef={React.createRef()}
          isQuickSearchEnabled={isQuickSearchEnabled}
          setIsQuickSearchEnabledAction={setIsQuickSearchEnabledAction}
          validationErrorMessage={validationErrorMessage}
          setValidationErrorMessage={setValidationErrorMessage}
        />
      }
    >
      <InboxScreen
        sections={sectionListData}
        isHeaderVisible={isHeaderVisible}
        onScrollToHideHeader={handleScrollToHideHeaderAction}
        onScrollToRevealHeader={handleScrollToRevealHeaderAction}
        isMultiSelectActive={isMultiSelectActive}
        selectedMessageIds={selectedMessageIds}
        onLongTapToSelectEmail={handleLongTapToSelectEmailAction}
        onTapToSelectAdditionalEmail={handleTapToSelectAdditionalEmailAction}
        onDeselectEmail={handleDeselectEmailAction}
        onSelectAllEmails={handleSelectAllEmailsAction}
        onClearAllSelections={handleClearAllSelectionsAction}
        onCancelMultiSelection={handleCancelMultiSelectionAction}
        onFlagPress={handleFlagMessageAction}
        handleTapMessage={handleTapMessage}
        handleRefreshList={fetchGridMessagesAction}
        isLoading={gridMessagesLoading}
        bottomSheetRef={bottomSheetRef}
        bottomSheetMenuState={bottomSheetMenuState}
        setBottomSheetMenuState={handleSetBottomSheetMenuStateAction}
        bottomSheetOptions={BOTTOM_SHEET_DEFAULT_OPTIONS}
        bottomSheetMoreOptions={BOTTOM_SHEET_MORE_OPTIONS}
        errorMessage={
          gridMessagesError ? "Something went wrong:\n" + gridMessagesError : ""
        }
        emptyMessage={
          appliedSearchFiltersCount > 0 ? "No results found" : "List Empty"
        }
        inboxListStyle={[styles.listContainer]}
        onLayout={() => {}}
        initialNumToRender={20}
        onScroll={() => {}}
        listFooterComponent={
          gridMessages.allIds.length < gridMessages.total ||
          gridMessagesLoadingMore ? (
            <View style={{ padding: 10 }}>
              {gridMessagesLoadingMore && (
                <ActivityIndicator size="small" color={color.MESSAGE_FLAG} />
              )}
            </View>
          ) : (
            <View style={{ padding: 10 }}>
              {emailIds?.length > 0 && (
                <CustomText style={{ textAlign: "center" }}>
                  No more items to show
                </CustomText>
              )}
            </View>
          )
        }
        loadMoreEmails={loadMoreMessages}
      />
    </SearchBarAnimationHOC>
  );
};

export default InboxHOC;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    searchBar: {
      justifyContent: "center",
      backgroundColor: "white",
      width: "100%",
      top: 0,
      zIndex: 1000,
    },
    input: {
      height: 40,
      backgroundColor: "#f0f0f0",
      borderRadius: 8,
      paddingHorizontal: 10,
    },
    listContainer: {
      flex: 1,
    },
    item: {
      padding: 20,
    },
    header: {
      padding: 10,
      backgroundColor: "#ddd",
    },
  });

  return { styles, color };
};
