/**
 * Helper functions for handling email signatures
 */

/**
 * Appends signature to email content
 * @param emailContent - The main email content
 * @param signatureHtml - The HTML signature to append
 * @param signatureText - The plain text signature to append (fallback)
 * @param isHtml - Whether the email content is HTML format
 * @returns The email content with signature appended
 */
export const appendSignatureToEmailContent = (
  emailContent: string,
  signatureHtml: string | null,
  signatureText: string | null,
  isHtml: boolean = true
): string => {
  // If no signature is available, return original content
  if (!signatureHtml && !signatureText) {
    return emailContent;
  }

  // Choose the appropriate signature format
  const signature = isHtml && signatureHtml ? signatureHtml : signatureText;
  
  if (!signature) {
    return emailContent;
  }

  // Add signature separator and signature
  const signatureSeparator = isHtml ? '<br><br>--<br>' : '\n\n--\n';
  
  return `${emailContent}${signatureSeparator}${signature}`;
};

/**
 * Gets the signature for a specific unibox
 * @param userUniboxes - The user uniboxes object from Redux state
 * @param selectedUniboxId - The ID of the selected unibox
 * @returns Object containing both HTML and text signatures
 */
export const getSignatureForUnibox = (
  userUniboxes: any,
  selectedUniboxId: string
): { signatureHtml: string | null; signatureText: string | null } => {
  const selectedUnibox = userUniboxes?.byId?.[selectedUniboxId];
  
  if (!selectedUnibox) {
    return { signatureHtml: null, signatureText: null };
  }

  return {
    signatureHtml: selectedUnibox.signatureHtml,
    signatureText: selectedUnibox.signatureText,
  };
};

/**
 * Gets the default signature from user uniboxes
 * @param userUniboxes - The user uniboxes object from Redux state
 * @returns Object containing both HTML and text signatures for the default unibox
 */
export const getDefaultSignature = (
  userUniboxes: any
): { signatureHtml: string | null; signatureText: string | null } => {
  // Find the default unibox
  const defaultUniboxId = userUniboxes?.allIds?.find((id: string) => 
    userUniboxes.byId[id]?.isSendMailDefault
  );

  if (!defaultUniboxId) {
    // If no default found, use the first available unibox
    const firstUniboxId = userUniboxes?.allIds?.[0];
    return getSignatureForUnibox(userUniboxes, firstUniboxId);
  }

  return getSignatureForUnibox(userUniboxes, defaultUniboxId);
};
