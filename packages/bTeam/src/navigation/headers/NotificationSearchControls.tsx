// components/NotificationSearchControls.tsx
import React, { useMemo } from "react";
import {
  SearchInput,
  AdvancedSearchButtons,
  SPACING,
  Theme,
  useThemeAwareObject,
} from "b-ui-lib";
import {
  NOTIFICATIONS_SEARCH_FIELD_INITIAL_VALUE,
  NOTIFICATIONS_SEARCH_FIELD_NAMES,
} from "../../constants/notificationsSearchFields";
import filterSearchSuggestions from "../../helpers/filterSearchSuggestions";
import { NotificationsSearchFields } from "../../containers/NotificationsSearchFiltersHOC";
import { StyleSheet, View } from "react-native";

type Props = {
  searchText: string;
  searchSuggestions: string[];
  searchFiltersCounter: number;
  isQuickSearchEnabled: boolean;
  setSearchFilters: (payload: NotificationsSearchFields) => void;
  setIsQuickSearchEnabled: (payload: boolean) => void;
  fetchGridNotifications: () => void;
  addSearchSuggestion: (suggestion: string) => void;
  clearNotificationsSearchFields: () => void;
  navigateToFilters: () => void;
};

const NotificationSearchControls = ({
  searchText,
  searchSuggestions,
  searchFiltersCounter,
  isQuickSearchEnabled,
  setSearchFilters,
  setIsQuickSearchEnabled,
  fetchGridNotifications,
  addSearchSuggestion,
  clearNotificationsSearchFields,
  navigateToFilters,
}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  const filteredSuggestions = useMemo(
    () => filterSearchSuggestions(searchSuggestions, searchText),
    [searchSuggestions, searchText]
  );

  const handleSearchInputChange = (textValue: string) => {
    setSearchFilters({
      ...NOTIFICATIONS_SEARCH_FIELD_INITIAL_VALUE,
      [NOTIFICATIONS_SEARCH_FIELD_NAMES.searchText]: textValue,
    });
    setIsQuickSearchEnabled(true);
  };

  const handleSearchInputClear = () => {
    if (searchFiltersCounter === 1) {
      return clearNotificationsSearchFields();
    }

    setSearchFilters({ [NOTIFICATIONS_SEARCH_FIELD_NAMES.searchText]: "" });
    fetchGridNotifications();
  };

  const handleDebounceFetchGridNotifications = (latestSearchText: string) => {
    fetchGridNotifications();
    addSearchSuggestion(latestSearchText);
  };

  const handleSuggestionPress = (suggestion: string) => {
    setSearchFilters({
      [NOTIFICATIONS_SEARCH_FIELD_NAMES.searchText]: suggestion,
    });

    fetchGridNotifications();
  };

  return (
    <View style={styles.container}>
      <SearchInput
        value={searchText}
        containerStyle={styles.searchInput}
        onChangeText={handleSearchInputChange}
        handleDebounceFunction={handleDebounceFetchGridNotifications}
        handleInputClear={handleSearchInputClear}
        placeholder={"Search here"}
        suggestions={filteredSuggestions}
        handleSuggestionPress={handleSuggestionPress}
      />

      <AdvancedSearchButtons
        searchFiltersCount={isQuickSearchEnabled ? 0 : searchFiltersCounter}
        isClearAllButtonVisible={
          !isQuickSearchEnabled && searchFiltersCounter > 0
        }
        handleClearAll={clearNotificationsSearchFields}
        handlePressCriteriaButton={navigateToFilters}
        style={{ buttonsContainer: styles.buttonsHeader }}
      />
    </View>
  );
};

export default NotificationSearchControls;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: color.BACKGROUND,
    },
    searchInput: {
      marginVertical: SPACING.XXS,
      marginHorizontal: SPACING.M,
      zIndex: 1000,
    },
    buttonsHeader: {
      paddingVertical: SPACING.XXS,
      paddingHorizontal: SPACING.M,
    },
  });

  return { styles, color };
};
