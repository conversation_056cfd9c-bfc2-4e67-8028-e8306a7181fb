import React, {
  useState,
  useMemo,
  useEffect,
  useRef,
  useCallback,
} from "react";
import { ActivityIndicator, StyleSheet, View, Pressable } from "react-native";

// Components
import {
  CustomText,
  FONT_SIZES,
  ICON_POSITIONS,
  IconTextButton,
  SPACING,
  Theme,
  useThemeAwareObject,
} from "b-ui-lib";
import MessageBottomButtons from "../../../general/MessageBottomButtons";
import HtmlMessageContent from "./HtmlMessageContent";
import RepliesContent from "./RepliesContent";

const NESTED_TABS_OPTIONS = {
  details: "details",
  replies: "replies",
} as const;

type NestedTabOption = keyof typeof NESTED_TABS_OPTIONS;

type Props = {
  html: string;
  width: number;
  replies: any;
  getMessageBodyLoading: boolean;
  getMessageActionsLoading: boolean;
  handleRefreshList: () => void;
  getMessageActionsError: string;
  messageBodyError: string;
  handleRetryMessageBody: () => void;
  isDraft: boolean;
  handleEditDraftMessage: () => void;
  onPressReply: () => void;
  onPressReplyAll: () => void;
  onPressForward: () => void;
};

const HtmlTab = ({
  html,
  width,
  replies,
  getMessageBodyLoading,
  getMessageActionsLoading,
  handleRefreshList,
  getMessageActionsError,
  messageBodyError,
  handleRetryMessageBody,
  isDraft,
  handleEditDraftMessage,
  onPressReply,
  onPressReplyAll,
  onPressForward,
}: Props) => {
  const [nestedTabOption, setNestedTabOption] = useState<NestedTabOption>(
    NESTED_TABS_OPTIONS.details
  );
  const [showTimeoutRetry, setShowTimeoutRetry] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const { styles, color } = useThemeAwareObject(createStyles);

  // Wrapper function for retry that resets timeout state
  const handleRetryWithReset = useCallback(() => {
    console.log("🔄 Retry button pressed - resetting timeout state");

    // Clear timeout and reset state immediately
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setShowTimeoutRetry(false);

    // Call the original retry function
    handleRetryMessageBody();
  }, [handleRetryMessageBody]);

  // Track loading timeout - show retry button after 5 seconds of loading without error
  useEffect(() => {
    console.log("⏱️ Timeout effect triggered:", {
      getMessageBodyLoading,
      messageBodyError: !!messageBodyError,
      showTimeoutRetry,
    });

    // Clear any existing timeout first
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    // Reset retry button state
    setShowTimeoutRetry(false);

    if (getMessageBodyLoading && !messageBodyError) {
      console.log("⏱️ Starting 5-second timeout timer");
      // Start timeout timer
      timeoutRef.current = setTimeout(() => {
        console.log("⏱️ 5-second timeout reached - showing retry button");
        setShowTimeoutRetry(true);
      }, 7000); // 5 seconds
    }

    // Cleanup on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, [getMessageBodyLoading, messageBodyError]);

  const onDetailsTabPress = () =>
    setNestedTabOption(NESTED_TABS_OPTIONS.details);
  const onRepliesTabPress = () =>
    setNestedTabOption(NESTED_TABS_OPTIONS.replies);

  // Memoize buttons to prevent unnecessary re-renders
  const BUTTONS = useMemo(
    () =>
      isDraft
        ? [
            {
              title: "Edit",
              isDisabled: false,
              onPress: handleEditDraftMessage,
            },
          ]
        : [
            {
              title: "Reply",
              isDisabled: false,
              onPress: onPressReply,
            },
            {
              title: "Reply All",
              isDisabled: false,
              onPress: onPressReplyAll,
            },
            {
              title: "Forward",
              isDisabled: false,
              onPress: onPressForward,
            },
          ],
    [
      isDraft,
      handleEditDraftMessage,
      onPressReply,
      onPressReplyAll,
      onPressForward,
    ]
  );

  // Render the appropriate content based on selected tab
  const renderContent = () => {
    if (nestedTabOption === NESTED_TABS_OPTIONS.details) {
      return (
        <HtmlMessageContent
          html={html}
          width={width}
          messageBodyError={messageBodyError}
          handleRetryMessageBody={handleRetryWithReset}
        />
      );
    }

    return (
      <RepliesContent
        replies={replies}
        getMessageActionsLoading={getMessageActionsLoading}
        handleRefreshList={handleRefreshList}
        getMessageActionsError={getMessageActionsError}
      />
    );
  };

  // Always return the same component structure to maintain consistent hook count
  return (
    <View style={styles.container}>
      {getMessageBodyLoading ? (
        // Loading state - same container structure to maintain consistent rendering
        <View
          style={[
            styles.body,
            { alignItems: "center", justifyContent: "center" },
          ]}
        >
          <ActivityIndicator size="large" color={color.BRAND_BLUE} />

          {showTimeoutRetry && (
            <View style={styles.timeoutRetryContainer}>
              <CustomText style={styles.timeoutText}>
                Taking longer than expected...
              </CustomText>
              <IconTextButton
                iconPosition={ICON_POSITIONS.left}
                iconName="Redo"
                iconSize={20}
                iconColor={color.BRAND_BLUE}
                title="Retry"
                onPress={handleRetryWithReset}
                textStyle={styles.retryButtonText}
                containerStyle={styles.retryButton}
              />
            </View>
          )}
        </View>
      ) : (
        // Normal state - same container structure
        <View style={styles.body}>
          <View style={styles.nestedTabsContainer}>
            <Pressable onPress={onDetailsTabPress}>
              <CustomText
                style={[
                  styles.tabText,
                  nestedTabOption === NESTED_TABS_OPTIONS.details &&
                    styles.activeTabText,
                ]}
              >
                Details
              </CustomText>
            </Pressable>
            <Pressable onPress={onRepliesTabPress}>
              <CustomText
                style={[
                  styles.tabText,
                  nestedTabOption === NESTED_TABS_OPTIONS.replies &&
                    styles.activeTabText,
                ]}
              >
                Replies ({replies?.length || 0})
              </CustomText>
            </Pressable>
          </View>

          <View style={styles.contentContainer}>{renderContent()}</View>
        </View>
      )}

      {!getMessageBodyLoading && <MessageBottomButtons buttons={BUTTONS} />}
    </View>
  );
};

export default React.memo(HtmlTab);

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
    },
    body: {
      flex: 1,
      paddingTop: SPACING.M,
      paddingHorizontal: SPACING.M,
      gap: 6,
    },
    nestedTabsContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginVertical: SPACING.SIX,
      borderBottomWidth: 1,
      borderBottomColor: color.BORDER_COLOR_FORM,
      paddingBottom: SPACING.S,
    },
    tabText: {
      fontSize: FONT_SIZES.FOURTEEN,
      fontWeight: "400",
      color: color.TEXT_DIMMED,
      paddingVertical: SPACING.S,
      paddingHorizontal: SPACING.M,
    },
    activeTabText: {
      fontWeight: "700",
      color: color.TEXT_DEFAULT_ORANGE,
    },
    contentContainer: {
      flex: 1,
    },
    timeoutRetryContainer: {
      marginTop: SPACING.L,
      alignItems: "center",
      paddingHorizontal: SPACING.L,
    },
    timeoutText: {
      fontSize: FONT_SIZES.FOURTEEN,
      color: color.TEXT_DIMMED,
      textAlign: "center",
      marginBottom: SPACING.M,
    },
    retryButton: {
      paddingHorizontal: SPACING.L,
      paddingVertical: SPACING.S,
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: color.BRAND_BLUE,
    },
    retryButtonText: {
      fontSize: FONT_SIZES.FOURTEEN,
      fontWeight: "600",
      color: color.BRAND_BLUE,
    },
  });

  return { styles, color };
};
