import React, { memo } from "react";
import {
  RefreshControl,
  StyleSheet,
  View,
} from "react-native";
import ContentLoader, { Rect, Circle } from "react-content-loader/native";

// Components
import {
  SPACING,
  Theme,
  useThemeAwareObject,
  MessageReplyList,
} from "b-ui-lib";
import EmptyTabBody from "../../../general/EmptyTabBody";
import TabErrorMessage from "../../TabErrorMessage";

type Props = {
  replies: any;
  getMessageActionsLoading: boolean;
  handleRefreshList: () => void;
  getMessageActionsError: string;
};

const RepliesContent = memo<Props>(({
  replies,
  getMessageActionsLoading,
  handleRefreshList,
  getMessageActionsError,
}) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  if (getMessageActionsLoading) {
    return (
      <View style={styles.loadingContainer}>
        {[...Array(3)].map((_, index) => (
          <ContentLoader
            key={index}
            width="100%"
            height={80}
            speed={1}
            backgroundColor={color.SKELETON_BACKGROUND}
            foregroundColor={color.SKELETON_FOREGROUND}
            style={styles.skeleton}
          >
            <Circle cx="16" cy="16" r="16" />
            <Rect x="43" y="3" rx="4" ry="4" width="50" height="13" />
            <Rect x="43" y="22" rx="4" ry="4" width="80" height="13" />
          </ContentLoader>
        ))}
      </View>
    );
  }

  if (replies?.length > 0) {
    return (
      <>
        <MessageReplyList
          replies={replies}
          refreshControl={
            <RefreshControl
              refreshing={getMessageActionsLoading}
              onRefresh={handleRefreshList}
              tintColor={color.TEXT_DIMMED}
            />
          }
        />
        <TabErrorMessage
          text={getMessageActionsError}
          isVisible={Boolean(getMessageActionsError)}
        />
      </>
    );
  }

  return (
    <>
      <EmptyTabBody
        iconName="check-circle"
        emptyMessage="No replies yet"
      />
      <TabErrorMessage
        text={getMessageActionsError}
        isVisible={Boolean(getMessageActionsError)}
      />
    </>
  );
});

RepliesContent.displayName = 'RepliesContent';

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    loadingContainer: {
      flex: 1,
    },
    skeleton: {
      marginVertical: SPACING.S,
    },
  });

  return { styles, color };
};

export default RepliesContent;