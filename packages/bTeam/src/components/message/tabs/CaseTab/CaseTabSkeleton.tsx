import React from "react";
import { View } from "react-native";
import ContentLoader, { Rect } from "react-content-loader/native";
import { useThemeAwareObject, Theme, SPACING } from "b-ui-lib";

type Props = {
  width: number;
};

const CaseTabSkeleton: React.FC<Props> = ({ width }) => {
  const { color } = useThemeAwareObject((theme: Theme) => ({
    color: theme.color,
  }));

  const contentWidth = width - SPACING.M * 2; // Account for horizontal padding

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
        paddingTop: SPACING.M,
        paddingHorizontal: SPACING.M,
      }}
    >
      <ContentLoader
        width={contentWidth}
        height={600}
        speed={1}
        backgroundColor={color.SKELETON_BACKGROUND}
        foregroundColor={color.SKELETON_FOREGROUND}
      >
        {/* Metadata section title */}
        <Rect x="0" y="0" rx="4" ry="4" width="120" height="16" />
        <Rect x="130" y="2" rx="8" ry="8" width="24" height="12" />

        {/* Metadata items (3 skeleton items) */}
        <Rect x="0" y="35" rx="4" ry="4" width={contentWidth * 0.7} height="14" />
        <Rect x="0" y="55" rx="4" ry="4" width={contentWidth * 0.5} height="12" />
        
        <Rect x="0" y="80" rx="4" ry="4" width={contentWidth * 0.8} height="14" />
        <Rect x="0" y="100" rx="4" ry="4" width={contentWidth * 0.6} height="12" />
        
        <Rect x="0" y="125" rx="4" ry="4" width={contentWidth * 0.6} height="14" />
        <Rect x="0" y="145" rx="4" ry="4" width={contentWidth * 0.4} height="12" />

        {/* Cases section title */}
        <Rect x="0" y="190" rx="4" ry="4" width="80" height="16" />
        <Rect x="90" y="192" rx="8" ry="8" width="20" height="12" />

        {/* Case items (2 detailed skeleton case cards) */}
        {/* Case 1 - More detailed structure */}
        <Rect x="0" y="225" rx="8" ry="8" width={contentWidth} height="80" />
        {/* Case 1 internal elements */}
        <Rect x="12" y="237" rx="4" ry="4" width={contentWidth * 0.6} height="14" />
        <Rect x="12" y="255" rx="4" ry="4" width={contentWidth * 0.4} height="12" />
        <Rect x="12" y="273" rx="4" ry="4" width={contentWidth * 0.3} height="10" />
        <Rect x={contentWidth - 60} y="280" rx="12" ry="12" width="48" height="16" />
        
        {/* Case 2 - More detailed structure */}
        <Rect x="0" y="320" rx="8" ry="8" width={contentWidth} height="80" />
        {/* Case 2 internal elements */}
        <Rect x="12" y="332" rx="4" ry="4" width={contentWidth * 0.5} height="14" />
        <Rect x="12" y="350" rx="4" ry="4" width={contentWidth * 0.7} height="12" />
        <Rect x="12" y="368" rx="4" ry="4" width={contentWidth * 0.35} height="10" />
        <Rect x={contentWidth - 60} y="375" rx="12" ry="12" width="48" height="16" />

        {/* Bottom button area */}
        <Rect x="0" y="420" rx="8" ry="8" width={contentWidth} height="48" />
      </ContentLoader>
    </View>
  );
};

export default CaseTabSkeleton;