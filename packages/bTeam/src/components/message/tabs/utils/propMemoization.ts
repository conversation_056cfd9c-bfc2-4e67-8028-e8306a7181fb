import { useMemo } from 'react';

/**
 * Utility to create stable prop objects for tab components
 * Helps prevent unnecessary re-renders by memoizing complex prop objects
 */

export interface TabProps {
  width: number;
  getMessageBodyLoading: boolean;
  getMessageActionsLoading: boolean;
  getMessageCommentsLoading?: boolean;
  getMessageActionsError: string;
  getMessageCommentsError?: string;
  messageBodyError: string;
  copyOrMoveMessageFolderError: string;
  isDraft: boolean;
  isAnyFileDownloadLoading: boolean;
  postMessageCommentLoading?: boolean;
  postMessageCommentError?: string;
  postMessageCommentSuccess?: boolean;
  uploadAttachmentLoading?: boolean;
  uploadAttachmentError?: string;
  attachmentsLength?: number;
  fetchNotifiedUsersLoading?: boolean;
  fetchNotifiedUsersError?: string;
}

export interface HtmlTabProps extends TabProps {
  html: string;
  replies: any;
}

export interface CommentTabProps extends TabProps {
  comments: any;
  recipientsEmails: any;
  notifiedUsers?: any;
}

export interface AttachmentTabProps extends TabProps {
  messageAttachmentsIds: any;
  attachments: any;
  attachmentsCount: number;
  downloadedAttachments: any;
}

export interface FolderTabProps extends TabProps {
  foldersIds: string[];
  messageFolders: any;
}

export interface CaseTabProps extends TabProps {
  caseMetadata: any;
  cases: any;
}

/**
 * Memoizes HTML tab props
 */
export const useHtmlTabProps = (baseProps: TabProps, html: string, replies: any): HtmlTabProps => {
  return useMemo(() => ({
    ...baseProps,
    html,
    replies,
  }), [
    baseProps.width,
    baseProps.getMessageBodyLoading,
    baseProps.getMessageActionsLoading,
    baseProps.getMessageActionsError,
    baseProps.messageBodyError,
    baseProps.isDraft,
    html,
    replies?.length, // Only track length to avoid deep comparison
  ]);
};

/**
 * Memoizes comment tab props
 */
export const useCommentTabProps = (
  baseProps: TabProps, 
  comments: any, 
  recipientsEmails: any,
  notifiedUsers?: any
): CommentTabProps => {
  return useMemo(() => ({
    ...baseProps,
    comments,
    recipientsEmails,
    notifiedUsers,
  }), [
    baseProps.width,
    baseProps.getMessageActionsLoading,
    baseProps.postMessageCommentLoading,
    baseProps.postMessageCommentError,
    baseProps.postMessageCommentSuccess,
    baseProps.uploadAttachmentLoading,
    baseProps.uploadAttachmentError,
    baseProps.attachmentsLength,
    baseProps.fetchNotifiedUsersLoading,
    baseProps.fetchNotifiedUsersError,
    comments?.length,
    recipientsEmails?.length,
    notifiedUsers?.length,
  ]);
};

/**
 * Memoizes attachment tab props
 */
export const useAttachmentTabProps = (
  baseProps: TabProps,
  messageAttachmentsIds: any,
  attachments: any,
  attachmentsCount: number,
  downloadedAttachments: any
): AttachmentTabProps => {
  return useMemo(() => ({
    ...baseProps,
    messageAttachmentsIds,
    attachments,
    attachmentsCount,
    downloadedAttachments,
  }), [
    baseProps.width,
    baseProps.getMessageActionsLoading,
    baseProps.isAnyFileDownloadLoading,
    messageAttachmentsIds?.length,
    attachmentsCount,
    // Use JSON.stringify for downloadedAttachments as it's a simple object
    JSON.stringify(downloadedAttachments),
  ]);
};

/**
 * Memoizes folder tab props
 */
export const useFolderTabProps = (
  baseProps: TabProps,
  foldersIds: string[],
  messageFolders: any
): FolderTabProps => {
  return useMemo(() => ({
    ...baseProps,
    foldersIds,
    messageFolders,
  }), [
    baseProps.width,
    baseProps.getMessageActionsLoading,
    baseProps.copyOrMoveMessageFolderError,
    foldersIds?.length,
    // Assume messageFolders structure is stable or track specific keys
  ]);
};

/**
 * Memoizes case tab props
 */
export const useCaseTabProps = (
  baseProps: TabProps,
  caseMetadata: any,
  cases: any
): CaseTabProps => {
  return useMemo(() => ({
    ...baseProps,
    caseMetadata,
    cases,
  }), [
    baseProps.width,
    caseMetadata?.id, // Assume caseMetadata has an id field
    cases?.length,
  ]);
};

/**
 * Creates a stable base props object
 */
export const useBaseTabProps = (
  width: number,
  loadingStates: {
    getMessageBodyLoading: boolean;
    getMessageActionsLoading: boolean;
    getMessageCommentsLoading?: boolean;
    postMessageCommentLoading?: boolean;
    uploadAttachmentLoading?: boolean;
    fetchNotifiedUsersLoading?: boolean;
    isAnyFileDownloadLoading: boolean;
  },
  errorStates: {
    getMessageActionsError: string;
    getMessageCommentsError?: string;
    messageBodyError: string;
    copyOrMoveMessageFolderError: string;
    postMessageCommentError?: string;
    uploadAttachmentError?: string;
    fetchNotifiedUsersError?: string;
  },
  otherProps: {
    isDraft: boolean;
    postMessageCommentSuccess?: boolean;
    attachmentsLength?: number;
  }
): TabProps => {
  return useMemo(() => ({
    width,
    ...loadingStates,
    ...errorStates,
    ...otherProps,
  }), [
    width,
    loadingStates.getMessageBodyLoading,
    loadingStates.getMessageActionsLoading,
    loadingStates.getMessageCommentsLoading,
    loadingStates.postMessageCommentLoading,
    loadingStates.uploadAttachmentLoading,
    loadingStates.fetchNotifiedUsersLoading,
    loadingStates.isAnyFileDownloadLoading,
    errorStates.getMessageActionsError,
    errorStates.getMessageCommentsError,
    errorStates.messageBodyError,
    errorStates.copyOrMoveMessageFolderError,
    errorStates.postMessageCommentError,
    errorStates.uploadAttachmentError,
    errorStates.fetchNotifiedUsersError,
    otherProps.isDraft,
    otherProps.postMessageCommentSuccess,
    otherProps.attachmentsLength,
  ]);
};