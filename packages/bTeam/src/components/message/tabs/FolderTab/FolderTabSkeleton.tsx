import React from "react";
import { View } from "react-native";
import ContentLoader, { Rect } from "react-content-loader/native";
import { useThemeAwareObject, Theme, SPACING } from "b-ui-lib";

type Props = {
  width: number;
};

const FolderTabSkeleton: React.FC<Props> = ({ width }) => {
  const { color } = useThemeAwareObject((theme: Theme) => ({
    color: theme.color,
  }));

  const contentWidth = width - SPACING.M * 2; // Account for horizontal padding

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
        paddingTop: SPACING.M,
        paddingHorizontal: SPACING.M,
      }}
    >
      <ContentLoader
        width={contentWidth}
        height={600}
        speed={1}
        backgroundColor={color.SKELETON_BACKGROUND}
        foregroundColor={color.SKELETON_FOREGROUND}
      >
        {/* Folders section title with count */}
        <Rect x="0" y="0" rx="4" ry="4" width="100" height="16" />
        <Rect x="110" y="2" rx="8" ry="8" width="20" height="12" />
        
        {/* Title underline/border */}
        <Rect x="0" y="25" rx="0" ry="0" width={contentWidth} height="1" />

        {/* Folder 1 */}
        {/* Folder icon */}
        <Rect x="0" y="50" rx="4" ry="4" width="16" height="15" />
        {/* Folder name */}
        <Rect x="25" y="50" rx="4" ry="4" width={contentWidth * 0.6} height="15" />
        {/* Folder path/details */}
        <Rect x="0" y="72" rx="4" ry="4" width={contentWidth * 0.8} height="12" />

        {/* Folder 2 */}
        {/* Folder icon */}
        <Rect x="0" y="105" rx="4" ry="4" width="16" height="15" />
        {/* Folder name */}
        <Rect x="25" y="105" rx="4" ry="4" width={contentWidth * 0.5} height="15" />
        {/* Folder path/details */}
        <Rect x="0" y="127" rx="4" ry="4" width={contentWidth * 0.7} height="12" />

        {/* Folder 3 */}
        {/* Folder icon */}
        <Rect x="0" y="160" rx="4" ry="4" width="16" height="15" />
        {/* Folder name */}
        <Rect x="25" y="160" rx="4" ry="4" width={contentWidth * 0.4} height="15" />
        {/* Folder path/details */}
        <Rect x="0" y="182" rx="4" ry="4" width={contentWidth * 0.6} height="12" />

        {/* Folder 4 */}
        {/* Folder icon */}
        <Rect x="0" y="215" rx="4" ry="4" width="16" height="15" />
        {/* Folder name */}
        <Rect x="25" y="215" rx="4" ry="4" width={contentWidth * 0.7} height="15" />
        {/* Folder path/details */}
        <Rect x="0" y="237" rx="4" ry="4" width={contentWidth * 0.5} height="12" />

        {/* Folder 5 */}
        {/* Folder icon */}
        <Rect x="0" y="270" rx="4" ry="4" width="16" height="15" />
        {/* Folder name */}
        <Rect x="25" y="270" rx="4" ry="4" width={contentWidth * 0.55} height="15" />
        {/* Folder path/details */}
        <Rect x="0" y="292" rx="4" ry="4" width={contentWidth * 0.75} height="12" />

        {/* Bottom action buttons */}
        <Rect x="0" y="540" rx="8" ry="8" width={(contentWidth - 10) / 2} height="48" />
        <Rect x={(contentWidth + 10) / 2} y="540" rx="8" ry="8" width={(contentWidth - 10) / 2} height="48" />
      </ContentLoader>
    </View>
  );
};

export default FolderTabSkeleton;