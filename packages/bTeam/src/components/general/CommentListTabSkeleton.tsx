import React from "react";
import { View } from "react-native";
import ContentLoader, { Rect, Circle } from "react-content-loader/native";
import { useThemeAwareObject, Theme, SPACING } from "b-ui-lib";

type Props = {
  width: number;
};

const CommentListTabSkeleton: React.FC<Props> = ({ width }) => {
  const { color } = useThemeAwareObject((theme: Theme) => ({
    color: theme.color,
  }));

  const contentWidth = width - SPACING.M * 2; // Account for horizontal padding

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
        paddingTop: SPACING.M,
        paddingHorizontal: SPACING.M,
      }}
    >
      <ContentLoader
        width={contentWidth}
        height={600}
        speed={1}
        backgroundColor={color.SKELETON_BACKGROUND}
        foregroundColor={color.SKELETON_FOREGROUND}
      >
        {/* Comments section title with count */}
        <Rect x="0" y="0" rx="4" ry="4" width="120" height="16" />
        <Rect x="130" y="2" rx="8" ry="8" width="24" height="12" />

        {/* Comment 1 */}
        {/* Avatar and header */}
        <Circle cx="20" cy="50" r="16" />
        <Rect x="45" y="38" rx="4" ry="4" width={contentWidth * 0.4} height="12" />
        <Rect x="45" y="55" rx="4" ry="4" width={contentWidth * 0.25} height="10" />
        
        {/* Comment content */}
        <Rect x="0" y="80" rx="4" ry="4" width={contentWidth * 0.9} height="12" />
        <Rect x="0" y="98" rx="4" ry="4" width={contentWidth * 0.75} height="12" />
        <Rect x="0" y="116" rx="4" ry="4" width={contentWidth * 0.6} height="12" />
        
        {/* Action buttons (star, reply, participants) */}
        <Rect x="0" y="145" rx="12" ry="12" width="60" height="20" />
        <Rect x="70" y="145" rx="12" ry="12" width="50" height="20" />
        <Rect x="130" y="145" rx="12" ry="12" width="80" height="20" />

        {/* Comment 2 */}
        {/* Avatar and header */}
        <Circle cx="20" cy="205" r="16" />
        <Rect x="45" y="193" rx="4" ry="4" width={contentWidth * 0.35} height="12" />
        <Rect x="45" y="210" rx="4" ry="4" width={contentWidth * 0.3} height="10" />
        
        {/* Comment content */}
        <Rect x="0" y="235" rx="4" ry="4" width={contentWidth * 0.85} height="12" />
        <Rect x="0" y="253" rx="4" ry="4" width={contentWidth * 0.7} height="12" />
        
        {/* Action buttons */}
        <Rect x="0" y="280" rx="12" ry="12" width="60" height="20" />
        <Rect x="70" y="280" rx="12" ry="12" width="50" height="20" />
        <Rect x="130" y="280" rx="12" ry="12" width="80" height="20" />

        {/* Comment 3 (shorter) */}
        {/* Avatar and header */}
        <Circle cx="20" cy="340" r="16" />
        <Rect x="45" y="328" rx="4" ry="4" width={contentWidth * 0.45} height="12" />
        <Rect x="45" y="345" rx="4" ry="4" width={contentWidth * 0.2} height="10" />
        
        {/* Comment content */}
        <Rect x="0" y="370" rx="4" ry="4" width={contentWidth * 0.8} height="12" />
        
        {/* Action buttons */}
        <Rect x="0" y="395" rx="12" ry="12" width="60" height="20" />
        <Rect x="70" y="395" rx="12" ry="12" width="50" height="20" />
        <Rect x="130" y="395" rx="12" ry="12" width="80" height="20" />

        {/* Bottom button area */}
        <Rect x="0" y="540" rx="8" ry="8" width={contentWidth} height="48" />
      </ContentLoader>
    </View>
  );
};

export default CommentListTabSkeleton;