import React from "react";
import { StyleSheet, View } from "react-native";

// Components
import { Theme, useThemeAwareObject, SPACING, Button } from "b-ui-lib";

type Button = {
  title: string;
  onPress: () => void;
  isDisabled: boolean;
};

type Props = {
  buttons: Button[];
};

const MessageBottomButtons: React.FC = ({ buttons }: Props) => {
  const { styles } = useThemeAwareObject(createStyles);

  return (
    <View style={styles.container}>
      {buttons.map((button, index) => (
        <Button
          key={`${button.title}-${index}`}
          title={button.title}
          isDisabled={button.isDisabled}
          onPress={button.onPress}
          variant="secondaryWithGrayBackground"
          containerStyle={{
            flex: 1,
            paddingVertical: SPACING.XS,
          }}
        />
      ))}
    </View>
  );
};

export default MessageBottomButtons;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: color.MESSAGE_BUTTONS_BACKGROUND,
      flexDirection: "row",
      gap: SPACING.SIX,
      paddingHorizontal: SPACING.M,
      paddingVertical: SPACING.TEN,
      borderTopWidth: 1,
      borderBottomWidth: 1,
      borderColor: color.MODAL_BACKGROUND_COLOR,
    },
  });

  return { styles, color };
};
