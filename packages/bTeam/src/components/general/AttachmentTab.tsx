import React, { useCallback, useMemo } from "react";
import { RefreshControl, StyleSheet, View } from "react-native";
import { useFocusEffect } from "@react-navigation/native";
import ContentLoader, { Rect } from "react-content-loader/native";

// Components
import { Theme, useThemeAwareObject, SPACING, TabTitle } from "b-ui-lib";
import { FILE_DOWNLOAD_STATUS } from "bcomponents";
import MessageBottomButtons from "./MessageBottomButtons";
import EmptyTabBody from "./EmptyTabBody";
import VirtualizedAttachmentList from "./VirtualizedAttachmentList";
import { TEST_IDS } from "../../constants/testIds";

type Props = {
  token: string;
  attachmentsIds: string[];
  messageAttachments: any;
  attachmentsCount: number;
  downloadedAttachments: {};
  handleRefreshList: () => void;
  getMessageActionsLoading: boolean;
  clearDownloadedAttachments: () => void;
  handleDownloadAttachment: () => void;
  handleDownloadAllAttachments: ([]) => void;
  isAnyFileDownloadLoading: boolean;
};

const AttachmentTab: React.FC<Props> = ({
  attachmentsIds,
  messageAttachments,
  attachmentsCount,
  downloadedAttachments,
  handleRefreshList,
  getMessageActionsLoading,
  clearDownloadedAttachments,
  handleDownloadAttachment,
  handleDownloadAllAttachments,
  isAnyFileDownloadLoading,
}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  useFocusEffect(
    useCallback(() => {
      return () => clearDownloadedAttachments();
    }, [])
  );

  // Memoize buttons to prevent unnecessary re-renders
  const BUTTONS = useMemo(
    () => [
      {
        title: `Download All (${attachmentsCount})`,
        isDisabled: attachmentsCount <= 0 || isAnyFileDownloadLoading,
        onPress: async () => {
          if (attachmentsCount > 0 && !isAnyFileDownloadLoading) {
            await handleDownloadAllAttachments(attachmentsIds);
          }
        },
      },
    ],
    [
      attachmentsCount,
      isAnyFileDownloadLoading,
      handleDownloadAllAttachments,
      attachmentsIds,
    ]
  );

  // Memoize attachment data preparation
  const attachmentData = useMemo(() => {
    if (!attachmentsIds || !messageAttachments?.byId) return [];

    return attachmentsIds.map((attachmentId) => ({
      id: attachmentId,
      ...(messageAttachments.byId[attachmentId] || {}),
      isLoading:
        downloadedAttachments?.[attachmentId] === FILE_DOWNLOAD_STATUS.loading,
    }));
  }, [attachmentsIds, messageAttachments, downloadedAttachments]);

  // Memoize refresh control
  const refreshControl = useMemo(
    () => (
      <RefreshControl
        refreshing={getMessageActionsLoading}
        onRefresh={handleRefreshList}
        tintColor={color.TEXT_DIMMED}
      />
    ),
    [getMessageActionsLoading, handleRefreshList, color.TEXT_DIMMED]
  );

  return (
    <View style={styles.container}>
      <View style={styles.body}>
        <TabTitle title="Attachments" count={attachmentsCount?.toString()} />

        {getMessageActionsLoading && (
          <>
            {[...Array(3)].map((_, index) => (
              <ContentLoader
                key={index}
                width="100%"
                height={55}
                speed={1}
                backgroundColor={color.SKELETON_BACKGROUND}
                foregroundColor={color.SKELETON_FOREGROUND}
              >
                <Rect x="0" y="0" rx="4" ry="4" width={"100%"} height="40" />

                <Rect x="4%" y="10" rx="4" ry="4" width={"70%"} height="20" />

                <Rect
                  x={"90%"}
                  y="10"
                  rx="4"
                  ry="4"
                  width={20}
                  height="20"
                  fill={color.RED}
                  color={color.RED}
                  fillOpacity={0.5}
                />
              </ContentLoader>
            ))}
          </>
        )}

        {attachmentData.length > 0 && !getMessageActionsLoading && (
          <VirtualizedAttachmentList
            attachments={attachmentData}
            handleDownload={handleDownloadAttachment}
            refreshControl={refreshControl}
          />
        )}

        {attachmentData.length === 0 && !getMessageActionsLoading && (
          <EmptyTabBody
            testID={TEST_IDS.messageAttachmentTabEmptyMessage}
            iconName="paperclip"
            emptyMessage="No attachments included"
          />
        )}
      </View>

      <MessageBottomButtons buttons={BUTTONS} />
    </View>
  );
};

export default React.memo(AttachmentTab);

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    body: {
      flex: 1,
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
      paddingTop: SPACING.M,
      paddingHorizontal: SPACING.M,
      gap: SPACING.SIX,
    },
  });

  return { styles, color };
};
