import React, { useRef } from "react";
import { StyleSheet, View } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";

// Components
import {
  Theme,
  useThemeAwareObject,
  MessageCommentList,
  SPACING,
  TabTitle,
} from "b-ui-lib";
import EmptyTabBody from "./EmptyTabBody";
import CommentBottomSheet, {
  CommentBottomSheetRef,
} from "./CommentBottomSheet";
import CommentListTabSkeleton from "./CommentListTabSkeleton";

// Types
import { MessageComment } from "../../types/message";
import { RelatedUsersList } from "../../types/userMails";
import MessageBottomButtons from "./MessageBottomButtons";

type newCommentPayload = {
  CMM_CMM_Guid?: string;
  notifyUsers: string[];
  description: string;
  restricted: 0 | 1;
};

type Props = {
  comments: MessageComment[];
  recipientsEmails: RelatedUsersList;
  notifiedUsers?: RelatedUsersList;
  handleStarComment: () => void;
  handlePostNewComment: ({
    CMM_CMM_Guid,
    notifyUsers,
    description,
    restricted,
  }: newCommentPayload) => void;
  postMessageCommentLoading: boolean;
  postMessageCommentError: string;
  postMessageCommentSuccess: boolean;
  handlePostMessageCommentSuccessDismiss: () => void;
  handleAddAttachment: () => void;
  uploadAttachmentLoading: boolean;
  uploadAttachmentError: string;
  attachmentsLength: number;
  attachments: any;
  handleDownloadAttachment: () => void;
  handleDownloadAllAttachments: () => void;
  fetchNotifiedUsers: () => void;
  fetchNotifiedUsersLoading: boolean;
  fetchNotifiedUsersError: string;
  width?: number;
  isLoading?: boolean;
};

const CommentListTab: React.FC<Props> = ({
  comments,
  recipientsEmails,
  notifiedUsers,
  handleStarComment,
  handlePostNewComment,
  postMessageCommentLoading,
  postMessageCommentError,
  postMessageCommentSuccess,
  handlePostMessageCommentSuccessDismiss,
  handleAddAttachment,
  uploadAttachmentLoading,
  uploadAttachmentError,
  attachmentsLength,
  attachments,
  handleDownloadAttachment,
  handleDownloadAllAttachments,
  fetchNotifiedUsers,
  fetchNotifiedUsersLoading,
  fetchNotifiedUsersError,
  width = 400,
  isLoading = false,
}) => {
  const { styles } = useThemeAwareObject(createStyles);
  const bottomSheetRef = useRef<CommentBottomSheetRef>(null);

  const onPressReply = (id: string) => {
    bottomSheetRef.current?.openForReply(id);
  };

  const onPressNewComment = () => {
    bottomSheetRef.current?.openForNewComment();
  };

  const BUTTONS = [
    {
      title: "Add new comment",
      onPress: onPressNewComment,
    },
  ];

  // Show skeleton while loading
  if (isLoading) {
    return <CommentListTabSkeleton width={width} />;
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View style={styles.container}>
        <View style={{ paddingHorizontal: SPACING.M, flex: 1 }}>
          <TabTitle title="Comments" count={comments?.length?.toString()} />

          {comments && comments?.length > 0 ? (
            <MessageCommentList
              comments={comments}
              attachments={attachments}
              handlePressReply={onPressReply}
              handlePressStar={handleStarComment}
              handleDownloadAttachment={handleDownloadAttachment}
              handleDownloadAllAttachments={handleDownloadAllAttachments}
              fetchNotifiedUsers={fetchNotifiedUsers}
              fetchNotifiedUsersLoading={fetchNotifiedUsersLoading}
              fetchNotifiedUsersError={fetchNotifiedUsersError}
              participantsList={notifiedUsers || []}
            />
          ) : (
            <EmptyTabBody iconName="message" emptyMessage="No Comments yet" />
          )}
        </View>

        <MessageBottomButtons buttons={BUTTONS} />
      </View>

      <CommentBottomSheet
        ref={bottomSheetRef}
        recipientsEmails={recipientsEmails}
        handlePostNewComment={handlePostNewComment}
        postMessageCommentLoading={postMessageCommentLoading}
        postMessageCommentError={postMessageCommentError}
        postMessageCommentSuccess={postMessageCommentSuccess}
        handlePostMessageCommentSuccessDismiss={
          handlePostMessageCommentSuccessDismiss
        }
        handleAddAttachment={handleAddAttachment}
        uploadAttachmentLoading={uploadAttachmentLoading}
        uploadAttachmentError={uploadAttachmentError}
        attachmentsLength={attachmentsLength}
      />
    </GestureHandlerRootView>
  );
};

export default React.memo(CommentListTab);

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
      paddingTop: SPACING.M,
      gap: SPACING.SIX,
    },
  });

  return { styles, color };
};
