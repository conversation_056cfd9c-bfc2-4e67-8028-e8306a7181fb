import React, { useState, useRef, useEffect, forwardRef, useImperativeHandle } from "react";
import BottomSheet from "@gorhom/bottom-sheet";

// Components
import AddComment from "./AddComment";
import { RelatedUsersList } from "../../types/userMails";
import SelectRecipientsBottomSheet from "../toBeMovedToUILib/SelectRecipientsBottomSheet";

const BOTTOM_SHEET_MENU_STATES = {
  addComment: "addComment",
  selectRecipient: "selectRecipient",
};

const SCROLL_VIEW_HEIGHT = 180;

type BottomSheetMenuState =
  (typeof BOTTOM_SHEET_MENU_STATES)[keyof typeof BOTTOM_SHEET_MENU_STATES];

type newCommentPayload = {
  CMM_CMM_Guid?: string;
  notifyUsers: string[];
  description: string;
  restricted: 0 | 1;
};

type Props = {
  recipientsEmails: RelatedUsersList;
  handlePostNewComment: ({
    CMM_CMM_Guid,
    notifyUsers,
    description,
    restricted,
  }: newCommentPayload) => void;
  postMessageCommentLoading: boolean;
  postMessageCommentError: string;
  postMessageCommentSuccess: boolean;
  handlePostMessageCommentSuccessDismiss: () => void;
  handleAddAttachment: () => void;
  uploadAttachmentLoading: boolean;
  uploadAttachmentError: string;
  attachmentsLength: number;
};

export type CommentBottomSheetRef = {
  openForNewComment: () => void;
  openForReply: (commentId: string) => void;
  close: () => void;
};

const CommentBottomSheet = forwardRef<CommentBottomSheetRef, Props>(({
  recipientsEmails,
  handlePostNewComment,
  postMessageCommentLoading,
  postMessageCommentError,
  postMessageCommentSuccess,
  handlePostMessageCommentSuccessDismiss,
  handleAddAttachment,
  uploadAttachmentLoading,
  uploadAttachmentError,
  attachmentsLength,
}, ref) => {
  const [bottomSheetMenuState, setBottomSheetMenuState] =
    useState<BottomSheetMenuState>(BOTTOM_SHEET_MENU_STATES.addComment);
  const [replyCommentId, setReplyCommentId] = useState("");
  const [selectedRecipientEmailIds, setSelectedRecipientEmailIds] = useState<
    string[]
  >([]);
  const [comment, setComment] = useState<string>("");
  const [isRestricted, setIsRestricted] = useState<0 | 1>(0);
  const bottomSheetRef = useRef<BottomSheet>(null);

  useImperativeHandle(ref, () => ({
    openForNewComment: () => {
      setReplyCommentId("");
      bottomSheetRef.current?.expand();
    },
    openForReply: (commentId: string) => {
      setReplyCommentId(commentId);
      bottomSheetRef.current?.expand();
    },
    close: () => {
      bottomSheetRef.current?.close();
    },
  }));

  const handleBottomSheetBackPress = () => {
    if (bottomSheetMenuState === BOTTOM_SHEET_MENU_STATES.selectRecipient) {
      setBottomSheetMenuState(BOTTOM_SHEET_MENU_STATES.addComment);
      return true;
    }

    if (bottomSheetMenuState === BOTTOM_SHEET_MENU_STATES.addComment) {
      bottomSheetRef.current?.close();
      return true;
    }

    return false;
  };

  useEffect(() => {
    if (!postMessageCommentLoading) {
      if (postMessageCommentSuccess) {
        bottomSheetRef.current?.close();
        handleResetNewCommentState();
        handlePostMessageCommentSuccessDismiss();
      } else if (postMessageCommentError) {
        alert(postMessageCommentError);
      }
    }
  }, [
    postMessageCommentLoading,
    postMessageCommentSuccess,
    postMessageCommentError,
  ]);

  const handleResetNewCommentState = () => {
    setComment("");
    setIsRestricted(0);
    setReplyCommentId("");
    setSelectedRecipientEmailIds([]);
  };

  const searchForRecipients = () =>
    setBottomSheetMenuState(BOTTOM_SHEET_MENU_STATES.selectRecipient);

  const handleCloseBottomSheet = () => bottomSheetRef.current?.close();

  const handleSheetChanges = (index: number) => {
    if (index === -1) {
      setBottomSheetMenuState(BOTTOM_SHEET_MENU_STATES.addComment);
    }
  };

  const handleSelectRecipientEmail = (id: string) => {
    setSelectedRecipientEmailIds((prev) =>
      prev.includes(id)
        ? prev.filter((emailId) => emailId !== id)
        : [...prev, id]
    );
  };

  const handleAddComment = () => {
    if (!selectedRecipientEmailIds.length) {
      alert("Please select users.");
      return;
    }
    if (!comment || comment.trim().length === 0) {
      alert("Please type a comment.");
      return;
    }

    handlePostNewComment({
      CMM_CMM_Guid: replyCommentId,
      notifyUsers: selectedRecipientEmailIds,
      description: comment,
      restricted: isRestricted,
    });
  };

  return (
    <SelectRecipientsBottomSheet
        scrollViewHeight={SCROLL_VIEW_HEIGHT}
        bottomSheetRef={bottomSheetRef}
        handleBackPress={handleBottomSheetBackPress}
        handleSheetChanges={handleSheetChanges}
        backPressDependencies={[bottomSheetMenuState, bottomSheetRef]}
        index={-1}
        enablePanDownToClose
        isLoading={postMessageCommentLoading}
        recipientsEmails={recipientsEmails}
        handleAddButton={() =>
          setBottomSheetMenuState(BOTTOM_SHEET_MENU_STATES.addComment)
        }
        selectedRecipientEmailId={selectedRecipientEmailIds}
        handleSelectRecipientEmail={handleSelectRecipientEmail}
        handleCloseBottomSheet={handleCloseBottomSheet}
        titleBoldText="Select"
        titleNormalText="Recipients"
      >
        {bottomSheetMenuState === BOTTOM_SHEET_MENU_STATES.addComment && (
          <AddComment
            selectedEmailText={
              Array.isArray(selectedRecipientEmailIds) // Check if it's an array
                ? recipientsEmails
                    .filter((email) =>
                      selectedRecipientEmailIds.includes(email.id)
                    ) // Filter emails matching the selected IDs
                    .map((email) => email.value) // Map to their values
                    .join(", ") // Join into a comma-separated string
                : recipientsEmails.find(
                    (email) => email.id === selectedRecipientEmailIds
                  )?.value // Handle single ID case
            }
            handleArrowPress={searchForRecipients}
            handleAddComment={handleAddComment}
            handleCloseBottomSheet={handleCloseBottomSheet}
            comment={comment}
            setComment={setComment}
            isRestricted={isRestricted}
            setIsRestricted={setIsRestricted}
            handleAddAttachment={handleAddAttachment}
            uploadAttachmentLoading={uploadAttachmentLoading}
            uploadAttachmentError={uploadAttachmentError}
            attachmentsLength={attachmentsLength}
          />
        )}
      </SelectRecipientsBottomSheet>
  );
});

CommentBottomSheet.displayName = 'CommentBottomSheet';

export default CommentBottomSheet;