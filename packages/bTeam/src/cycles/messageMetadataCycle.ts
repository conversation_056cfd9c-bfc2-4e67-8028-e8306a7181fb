import xs from "xstream/index";
import sampleCombine from "xstream/extra/sampleCombine";
import flattenConcurrently from "xstream/extra/flattenConcurrently";
import { getMessageMetadataFailed, getMessageMetadataSuccess, getMessageMetadata } from "../slices/gridMessageSlice.ts";

export const fetchMessageMetadata = (sources) => {
  const state$ = sources.STATE;
  const token$ = state$.map((state) => state?.persist?.bTeamAuth?.token);
  const domainBaseUrl$ = state$.map((state) => state?.persist?.bTeamAuth?.domainBaseUrl);

  const request$ = sources.ACTION.filter(
    (action) => action.type === getMessageMetadata.type)
    .compose(sampleCombine(token$, domainBaseUrl$))
    .map(([action, token, domainBaseUrl]) => {
      const { guid } = action?.payload || {};

      return {
        url: `${domainBaseUrl}/api/MetaFieldEntities2?MFE_EntityPK_Guid=${guid}&CMM_ENT_Id=1001&_=1734596386524`,
        category: "getMessageMetadata",
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };
    });

  const response$ = sources.HTTP.select("getMessageMetadata")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .compose(flattenConcurrently)
    .filter((response) => response.status === 200);

  const action$ = xs
    .combine(response$)
    .map(([response]) => {
      const messageBody = response?.text;
      const url = response.request.url
      const guidMatch = url.match(/MFE_EntityPK_Guid=([^&]+)/);
      const UMS_Guid = guidMatch ? guidMatch[1] : null;

      return getMessageMetadataSuccess({ UMS_Guid, messageBody });
    });

  return {
    ACTION: action$,
    HTTP: request$,
  };
};

export const fetchMessageMetadataFailed = (sources) => {
  const response$ = sources.HTTP.select("getMessageMetadata")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status !== 200);

  const action$ = xs
    .combine(response$)
    .map((arr) => getMessageMetadataFailed(arr));

  return {
    ACTION: action$,
  };
};
