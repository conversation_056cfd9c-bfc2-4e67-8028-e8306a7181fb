import xs from "xstream/index";
import sampleCombine from "xstream/extra/sampleCombine";
import { getUserUniboxesInfos, getUserUniboxesInfosFailed, getUserUniboxesInfosSuccess } from "../slices/usersMailDomainSlice";

export const fetchUniboxesInfos = (sources) => {
  const state$ = sources.STATE;
  const token$ = state$.map((state) => state?.persist?.bTeamAuth?.token);
  const domainBaseUrl$ = state$.map((state) => state?.persist?.bTeamAuth?.domainBaseUrl);

  const request$ = sources.ACTION.filter(
    (action) => action.type === getUserUniboxesInfos.type)
    .compose(sampleCombine(token$, domainBaseUrl$))
    .map(([action, token, domainBaseUrl]) => {

      return {
        url: `${domainBaseUrl}/api/UniboxInfos?UUN_SendMailEnabled=true&UNI_MST_Id=1`,
        category: "fetchUniboxesInfos",
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };
    });

  const response$ = sources.HTTP.select("fetchUniboxesInfos")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status === 200);

  const action$ = xs
    .combine(response$)
    .map(([response]) => {
      const messageBody = response?.text;

      return getUserUniboxesInfosSuccess(messageBody);
    });

  return {
    ACTION: action$,
    HTTP: request$,
  };
};

export const fetchUniboxesInfosFailed = (sources) => {
  const response$ = sources.HTTP.select("fetchUniboxesInfos")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status !== 200);

  const action$ = xs
    .combine(response$)
    .map((arr) => getUserUniboxesInfosFailed(arr));

  return {
    ACTION: action$,
  };
};
