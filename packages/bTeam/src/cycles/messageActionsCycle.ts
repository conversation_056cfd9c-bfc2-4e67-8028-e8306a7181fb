import xs from "xstream/index";
import sampleCombine from "xstream/extra/sampleCombine";
import flattenConcurrently from "xstream/extra/flattenConcurrently";
import {
  getMessageActions,
  getMessageActionsFailed,
  getMessageActionsSuccess,
} from "../slices/gridMessageSlice";
import { getMessageActionsAttachmentsSuccess } from "../slices/attachmentsSlice";

export const fetchMessageActions = (sources) => {
  const state$ = sources.STATE;
  const token$ = state$.map((state) => state?.persist?.bTeamAuth?.token);
  const domainBaseUrl$ = state$.map((state) => state?.persist?.bTeamAuth?.domainBaseUrl);

  const request$ = sources.ACTION.filter(
    (action) => action.type === getMessageActions.type
  )
    .compose(sampleCombine(token$, domainBaseUrl$))
    .map(([action, token, domainBaseUrl]) => {
      const { UMS_Guid } = action?.payload || {};

      return {
        url: `${domainBaseUrl}/api/MessageActions2?messageActionType=view&cleanupHtml=true&UMS_Guid=${UMS_Guid}`,
        category: "getMessageActions",
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };
    });

  const response$ = sources.HTTP.select("getMessageActions")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .compose(flattenConcurrently)
    .filter((response) => response.status === 200);

  const action1$ = xs.combine(response$).map(([response]) => {
    return getMessageActionsSuccess(response?.text);
  });
  const action2$ = xs.combine(response$).map(([response]) => {
    return getMessageActionsAttachmentsSuccess(response?.text);
  });

  return {
    ACTION: xs.merge(action1$, action2$),
    HTTP: request$,
  };
};

export const fetchMessageActionsFailed = (sources) => {
  const response$ = sources.HTTP.select("getMessageActions")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status !== 200);

  const action$ = xs
    .combine(response$)
    .map((arr) => getMessageActionsFailed(arr));

  return {
    ACTION: action$,
  };
};
