import xs from "xstream/index";
import sampleCombine from "xstream/extra/sampleCombine";
import { getMessageComments, getMessageCommentsFailed, getMessageCommentsSuccess } from "../slices/gridMessageSlice.ts";
import { getCommentsAttachmentsSuccess } from "../slices/attachmentsSlice.ts";
import { fetchMessageCommentsSuccess } from "../slices/commentsSlice.ts";
import { getCaseCommentsSuccess } from "../slices/casesSlice.ts";
import flattenConcurrently from "xstream/extra/flattenConcurrently";

export const fetchMessageComments = (sources) => {
  const state$ = sources.STATE;
  const token$ = state$.map((state) => state?.persist?.bTeamAuth?.token);
  const domainBaseUrl$ = state$.map((state) => state?.persist?.bTeamAuth?.domainBaseUrl);
  const actionPayload$ = sources.ACTION.filter(
    (action) => action.type === getMessageComments.type
  );

  const request$ = sources.ACTION.filter(
    (action) => action.type === getMessageComments.type)
    .compose(sampleCombine(token$, domainBaseUrl$))
    .map(([action, token, domainBaseUrl]) => {
      const { guid, entityId = 1001 } = action?.payload || {};

      return {
        url: `${domainBaseUrl}/api/CMM_Comments?CMM_EntityPK_Guid=${guid}&CMM_ENT_Id=${entityId}&pageSize=40&goToComment=undefined`,
        category: "getMessageComments",
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };
    });

  const response$ = sources.HTTP.select("getMessageComments")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .compose(flattenConcurrently)
    .filter((response) => response.status === 200);

  const action1$ = response$
    .compose(sampleCombine(actionPayload$))
    .map(([response, actionPayload]) => {
      const { guid, entityId } = actionPayload.payload;

      const messageBody = response?.text;

      if (entityId !== 1001) {
        return { type: "NoOperation" };
      }

      return getMessageCommentsSuccess({ UMS_Guid: guid, messageBody });
    });

  const action2$ = response$
    .compose(sampleCombine(actionPayload$))
    .map(([response, actionPayload]) => {
      const { guid, entityId } = actionPayload.payload;

      const messageBody = response?.text;

      if (entityId !== 1002) {
        return { type: "NoOperation" };
      }

      return getCaseCommentsSuccess({ guid, messageBody });
    });

  const action3$ = xs
    .combine(response$)
    .map(([response]) => {
      const messageBody = response?.text;

      return getCommentsAttachmentsSuccess({ messageBody });
    });


  const action4$ = xs
    .combine(response$)
    .map(([response]) => {
      const responseBody = JSON.parse(response?.text);

      return fetchMessageCommentsSuccess({ responseBody });
    });

  return {
    ACTION: xs.merge(action1$, action2$, action3$, action4$),
    HTTP: request$,
  };
};

export const fetchMessageCommentsFailed = (sources) => {
  const response$ = sources.HTTP.select("getMessageComments")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status !== 200);

  const action$ = xs
    .combine(response$)
    .map((arr) => getMessageCommentsFailed(arr));

  return {
    ACTION: action$,
  };
};
