import xs from "xstream/index";
import sampleCombine from "xstream/extra/sampleCombine";
import { getUserUniboxes, getUserUniboxesFailed, getUserUniboxesSuccess } from "../slices/usersMailDomainSlice";

export const fetchUniboxes = (sources) => {
  const state$ = sources.STATE;
  const token$ = state$.map((state) => state?.persist?.bTeamAuth?.token);
  const domainBaseUrl$ = state$.map((state) => state?.persist?.bTeamAuth?.domainBaseUrl);

  const request$ = sources.ACTION.filter(
    (action) => action.type === getUserUniboxes.type)
    .compose(sampleCombine(token$, domainBaseUrl$))
    .map(([action, token, domainBaseUrl]) => {

      return {
        url: `${domainBaseUrl}/api/UserUniboxes?mobileUserUnibox=`,
        category: "fetchUniboxes",
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };
    });

  const response$ = sources.HTTP.select("fetchUniboxes")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status === 200);

  const action$ = xs
    .combine(response$)
    .map(([response]) => {
      const messageBody = response?.text;

      return getUserUniboxesSuccess(messageBody);
    });

  return {
    ACTION: action$,
    HTTP: request$,
  };
};

export const fetchUniboxesFailed = (sources) => {
  const response$ = sources.HTTP.select("fetchUniboxes")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status !== 200);

  const action$ = xs
    .combine(response$)
    .map((arr) => getUserUniboxesFailed(arr));

  return {
    ACTION: action$,
  };
};
